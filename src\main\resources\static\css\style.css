/* Ubuntu Healthcare SA - Home Page Styles */

:root {
    /* South African Flag Colors */
    --sa-green: #007749;
    --sa-gold: #FFB612;
    --sa-blue: #002395;
    --sa-red: #DE3831;
    --sa-black: #000000;
    --sa-white: #FFFFFF;
    
    /* Ubuntu Theme Colors */
    --ubuntu-primary: #007749;
    --ubuntu-secondary: #FFB612;
    --ubuntu-accent: #002395;
    --ubuntu-light: #f8f9fa;
    --ubuntu-dark: #2c3e50;
    
    /* Gradients */
    --ubuntu-gradient: linear-gradient(135deg, var(--sa-green) 0%, var(--sa-blue) 100%);
    --gold-gradient: linear-gradient(135deg, var(--sa-gold) 0%, #FFA000 100%);
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: var(--ubuntu-dark);
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    min-height: 100vh;
}

/* Header Styles */
.ubuntu-header {
    background: var(--ubuntu-gradient);
    color: white;
    padding: 2rem 0;
    position: relative;
    overflow: hidden;
}

.ubuntu-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="50" cy="50" r="2" fill="rgba(255,255,255,0.1)"/></svg>') repeat;
    animation: float 20s infinite linear;
}

@keyframes float {
    0% { transform: translateY(0px); }
    100% { transform: translateY(-100px); }
}

.brand-badge {
    background: rgba(255, 255, 255, 0.2);
    padding: 0.5rem 1rem;
    border-radius: 25px;
    display: inline-block;
    margin-bottom: 1rem;
    font-size: 0.9rem;
    backdrop-filter: blur(10px);
}

.ubuntu-title {
    font-size: 3.5rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

.sawubona {
    color: var(--sa-gold);
}

.mzansi {
    color: white;
}

.ubuntu-subtitle {
    font-size: 1.3rem;
    opacity: 0.9;
}

.ubuntu-text {
    color: var(--sa-gold);
    font-weight: 600;
}

/* South African Flag Element */
.sa-flag-element {
    position: relative;
}

.flag-colors {
    display: flex;
    flex-direction: column;
    width: 80px;
    height: 60px;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 4px 15px rgba(0,0,0,0.2);
}

.flag-stripe {
    flex: 1;
}

.flag-stripe.green {
    background: var(--sa-green);
}

.flag-stripe.gold {
    background: var(--sa-gold);
}

.flag-stripe.blue {
    background: var(--sa-blue);
}

/* Hero Section */
.hero-section {
    padding: 4rem 0;
    background: white;
    position: relative;
}

.hero-content {
    padding: 2rem 0;
}

.hero-title {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--ubuntu-dark);
    margin-bottom: 1.5rem;
}

.ubuntu-highlight {
    color: var(--ubuntu-primary);
    position: relative;
}

.ubuntu-highlight::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 100%;
    height: 3px;
    background: var(--gold-gradient);
    border-radius: 2px;
}

.hero-description {
    font-size: 1.2rem;
    color: #666;
    margin-bottom: 2rem;
    line-height: 1.8;
}

.hero-stats {
    display: flex;
    gap: 2rem;
    margin-bottom: 2rem;
    flex-wrap: wrap;
}

.stat-item {
    text-align: center;
    padding: 1rem;
    background: rgba(0, 119, 73, 0.1);
    border-radius: 12px;
    min-width: 120px;
}

.stat-item i {
    font-size: 1.5rem;
    color: var(--ubuntu-primary);
    margin-bottom: 0.5rem;
    display: block;
}

.stat-number {
    font-size: 1.8rem;
    font-weight: 700;
    color: var(--ubuntu-primary);
    display: block;
}

.stat-label {
    font-size: 0.9rem;
    color: #666;
}

/* Buttons */
.btn-ubuntu-primary {
    background: var(--ubuntu-gradient);
    border: none;
    color: white;
    padding: 1rem 2rem;
    border-radius: 50px;
    font-weight: 600;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(0, 119, 73, 0.3);
}

.btn-ubuntu-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0, 119, 73, 0.4);
    color: white;
}

.btn-ubuntu-secondary {
    background: var(--gold-gradient);
    border: none;
    color: var(--ubuntu-dark);
    padding: 1rem 2rem;
    border-radius: 50px;
    font-weight: 600;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(255, 182, 18, 0.3);
}

.btn-ubuntu-secondary:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(255, 182, 18, 0.4);
    color: var(--ubuntu-dark);
}

.btn-outline-ubuntu {
    border: 2px solid var(--ubuntu-primary);
    color: var(--ubuntu-primary);
    background: transparent;
    padding: 1rem 2rem;
    border-radius: 50px;
    font-weight: 600;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    transition: all 0.3s ease;
}

.btn-outline-ubuntu:hover {
    background: var(--ubuntu-primary);
    color: white;
    transform: translateY(-2px);
}

/* Hero Image */
.hero-image {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 400px;
}

.healthcare-illustration {
    position: relative;
    width: 300px;
    height: 300px;
    display: flex;
    justify-content: center;
    align-items: center;
}

.pulse-animation {
    font-size: 4rem;
    color: var(--sa-red);
    animation: pulse 2s infinite;
    position: absolute;
}

@keyframes pulse {
    0% { transform: scale(1); opacity: 1; }
    50% { transform: scale(1.1); opacity: 0.7; }
    100% { transform: scale(1); opacity: 1; }
}

.ubuntu-circle {
    background: var(--ubuntu-gradient);
    width: 150px;
    height: 150px;
    border-radius: 50%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    color: white;
    font-weight: 600;
    box-shadow: 0 10px 30px rgba(0, 119, 73, 0.3);
    position: relative;
    z-index: 2;
}

.ubuntu-circle span {
    font-size: 1.2rem;
}

.ubuntu-circle small {
    font-size: 0.8rem;
    opacity: 0.8;
}

/* Features Section */
.features-section {
    padding: 4rem 0;
    background: var(--ubuntu-light);
}

.section-title {
    font-size: 2.2rem;
    font-weight: 700;
    color: var(--ubuntu-dark);
    margin-bottom: 1rem;
}

.section-subtitle {
    font-size: 1.1rem;
    color: #666;
    margin-bottom: 3rem;
}

.feature-card {
    background: white;
    padding: 2rem;
    border-radius: 15px;
    text-align: center;
    height: 100%;
    transition: all 0.3s ease;
    border: none;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.feature-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 30px rgba(0,0,0,0.15);
}

.ubuntu-card {
    border-top: 4px solid var(--ubuntu-primary);
}

.community-card {
    border-top: 4px solid var(--sa-gold);
}

.diversity-card {
    border-top: 4px solid var(--sa-blue);
}

.feature-icon {
    width: 80px;
    height: 80px;
    margin: 0 auto 1.5rem;
    background: var(--ubuntu-gradient);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 2rem;
}

.community-card .feature-icon {
    background: var(--gold-gradient);
    color: var(--ubuntu-dark);
}

.diversity-card .feature-icon {
    background: linear-gradient(135deg, var(--sa-blue) 0%, #1565C0 100%);
}

.feature-card h4 {
    font-size: 1.3rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: var(--ubuntu-dark);
}

.feature-card p {
    color: #666;
    line-height: 1.6;
}

/* Call to Action Section */
.cta-section {
    padding: 4rem 0;
    background: var(--ubuntu-gradient);
    color: white;
    text-align: center;
}

.cta-title {
    font-size: 2.2rem;
    font-weight: 700;
    margin-bottom: 1rem;
}

.cta-description {
    font-size: 1.2rem;
    margin-bottom: 2rem;
    opacity: 0.9;
}

/* Footer */
.ubuntu-footer {
    background: var(--ubuntu-dark);
    color: white;
    padding: 2rem 0;
    text-align: center;
}

.ubuntu-footer p {
    margin: 0;
    opacity: 0.8;
}

/* Responsive Design */
@media (max-width: 768px) {
    .ubuntu-title {
        font-size: 2.5rem;
    }
    
    .hero-title {
        font-size: 2rem;
    }
    
    .hero-stats {
        justify-content: center;
    }
    
    .stat-item {
        min-width: 100px;
    }
    
    .btn-ubuntu-primary,
    .btn-ubuntu-secondary,
    .btn-outline-ubuntu {
        padding: 0.8rem 1.5rem;
        font-size: 0.9rem;
    }
    
    .section-title {
        font-size: 1.8rem;
    }
    
    .cta-title {
        font-size: 1.8rem;
    }
}
