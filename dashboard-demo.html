<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Patient Dashboard - Ubuntu Healthcare SA</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            --sa-green: #007749;
            --sa-gold: #FFB612;
            --sa-blue: #002395;
            --sa-red: #DE3831;
            --ubuntu-gradient: linear-gradient(135deg, var(--sa-green) 0%, var(--sa-blue) 100%);
            --gold-gradient: linear-gradient(135deg, var(--sa-gold) 0%, #FFA000 100%);
        }

        body {
            font-family: 'Se<PERSON>e UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #f5f7fa;
            color: #2c3e50;
        }

        .ubuntu-navbar {
            background: white;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            padding: 1rem 0;
        }

        .navbar-brand {
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--sa-green) !important;
        }

        .welcome-card {
            background: var(--ubuntu-gradient);
            color: white;
            padding: 2rem;
            border-radius: 15px;
            margin-bottom: 2rem;
            box-shadow: 0 5px 20px rgba(0, 119, 73, 0.3);
        }

        .welcome-title {
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
        }

        .ubuntu-symbol {
            text-align: center;
            background: rgba(255, 255, 255, 0.2);
            padding: 1rem;
            border-radius: 50%;
            width: 80px;
            height: 80px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            backdrop-filter: blur(10px);
        }

        .chat-monitor-card {
            height: 500px;
            display: flex;
            flex-direction: column;
        }

        .bg-ubuntu {
            background: var(--ubuntu-gradient) !important;
        }

        .chat-container {
            flex: 1;
            overflow-y: auto;
            padding: 1rem;
            background: #f8f9fa;
        }

        .chat-message {
            margin-bottom: 1rem;
        }

        .bot-message .message-content {
            background: var(--sa-green);
            color: white;
            padding: 0.8rem 1rem;
            border-radius: 15px 15px 15px 5px;
            max-width: 80%;
        }

        .user-message .message-content {
            background: var(--sa-gold);
            color: #2c3e50;
            padding: 0.8rem 1rem;
            border-radius: 15px 15px 5px 15px;
            max-width: 80%;
            margin-left: auto;
        }

        .chat-input-container {
            padding: 1rem;
            background: white;
            border-top: 1px solid #dee2e6;
        }

        .btn-ubuntu {
            background: var(--sa-green);
            border: none;
            color: white;
        }

        .btn-ubuntu:hover {
            background: #005a37;
            color: white;
        }

        .section-title {
            font-size: 1.5rem;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 1rem;
            border-left: 4px solid var(--sa-green);
            padding-left: 1rem;
        }

        .quick-action-card {
            background: white;
            padding: 1.5rem;
            border-radius: 12px;
            text-align: center;
            height: 100%;
            transition: all 0.3s ease;
            cursor: pointer;
            border: 2px solid transparent;
            box-shadow: 0 3px 10px rgba(0,0,0,0.1);
        }

        .quick-action-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
            border-color: var(--sa-green);
        }

        .appointments-card { border-top: 4px solid var(--sa-green); }
        .medications-card { border-top: 4px solid var(--sa-gold); }
        .scanner-card { border-top: 4px solid var(--sa-blue); }
        .tips-card { border-top: 4px solid var(--sa-red); }

        .card-icon {
            width: 60px;
            height: 60px;
            margin: 0 auto 1rem;
            background: var(--ubuntu-gradient);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.5rem;
        }

        .medications-card .card-icon {
            background: var(--gold-gradient);
            color: #2c3e50;
        }

        .scanner-card .card-icon {
            background: linear-gradient(135deg, var(--sa-blue) 0%, #1565C0 100%);
        }

        .tips-card .card-icon {
            background: linear-gradient(135deg, var(--sa-red) 0%, #C62828 100%);
        }

        .health-card {
            border: none;
            border-radius: 12px;
            box-shadow: 0 3px 15px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
        }

        .health-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }

        .hypertension-card { border-left: 5px solid var(--sa-red); }
        .diabetes-card { border-left: 5px solid var(--sa-blue); }

        .health-reading {
            font-size: 1.5rem;
            font-weight: 700;
            margin: 0.5rem 0;
            color: #2c3e50;
        }

        .info-card {
            border: none;
            border-radius: 12px;
            box-shadow: 0 3px 15px rgba(0,0,0,0.1);
            margin-bottom: 1.5rem;
        }

        .medication-item {
            padding: 0.8rem 0;
            border-bottom: 1px solid #f1f3f4;
        }

        .medication-item:last-child { border-bottom: none; }

        .appointment-item {
            display: flex;
            align-items: center;
            padding: 1rem 0;
            border-bottom: 1px solid #f1f3f4;
        }

        .appointment-item:last-child { border-bottom: none; }

        .appointment-date {
            background: var(--ubuntu-gradient);
            color: white;
            padding: 0.8rem;
            border-radius: 10px;
            text-align: center;
            margin-right: 1rem;
            min-width: 60px;
        }

        .appointment-date .date {
            font-size: 1.2rem;
            font-weight: 700;
            display: block;
        }

        .appointment-date .month {
            font-size: 0.8rem;
            opacity: 0.9;
        }

        .report-stat {
            text-align: center;
            padding: 1rem;
            background: rgba(0, 119, 73, 0.05);
            border-radius: 10px;
            margin-bottom: 1rem;
        }

        .report-stat h4 {
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: 0.3rem;
        }

        .fade-in {
            animation: fadeInUp 0.6s ease-out forwards;
        }

        @keyframes fadeInUp {
            from { opacity: 0; transform: translateY(30px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .notification-toast {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 9999;
            min-width: 300px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
            border: none;
            border-radius: 10px;
        }

        @media (max-width: 768px) {
            .welcome-title { font-size: 1.5rem; }
            .chat-monitor-card { height: 400px; margin-bottom: 2rem; }
            .quick-action-card { margin-bottom: 1rem; }
        }
    </style>
</head>
<body>
    <!-- Top Navigation Bar -->
    <nav class="navbar navbar-expand-lg ubuntu-navbar">
        <div class="container-fluid">
            <div class="navbar-brand">
                <i class="fas fa-heart-pulse text-danger"></i>
                <span class="brand-text">Ubuntu Healthcare</span>
            </div>

            <!-- Language Selection -->
            <div class="navbar-nav me-auto">
                <div class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle language-selector" href="#" role="button" data-bs-toggle="dropdown" style="color: var(--sa-green); font-weight: 500;">
                        <i class="fas fa-globe"></i>
                        <span id="current-language">English</span>
                    </a>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item" href="#" data-lang="en">English</a></li>
                        <li><a class="dropdown-item" href="#" data-lang="zu">isiZulu</a></li>
                        <li><a class="dropdown-item" href="#" data-lang="xh">isiXhosa</a></li>
                        <li><a class="dropdown-item" href="#" data-lang="af">Afrikaans</a></li>
                        <li><a class="dropdown-item" href="#" data-lang="st">Sesotho</a></li>
                        <li><a class="dropdown-item" href="#" data-lang="tn">Setswana</a></li>
                        <li><a class="dropdown-item" href="#" data-lang="ve">Tshivenda</a></li>
                        <li><a class="dropdown-item" href="#" data-lang="ts">Xitsonga</a></li>
                        <li><a class="dropdown-item" href="#" data-lang="ss">siSwati</a></li>
                        <li><a class="dropdown-item" href="#" data-lang="nr">isiNdebele</a></li>
                        <li><a class="dropdown-item" href="#" data-lang="nso">Sepedi</a></li>
                    </ul>
                </div>
            </div>

            <!-- Right side navigation -->
            <div class="navbar-nav">
                <!-- Voice Call -->
                <button class="btn btn-outline-success me-2" id="voice-call-btn">
                    <i class="fas fa-phone"></i>
                    <span class="d-none d-md-inline">Voice Call</span>
                </button>

                <!-- Emergency Support -->
                <button class="btn btn-danger me-2" id="emergency-btn" data-bs-toggle="modal" data-bs-target="#emergencyModal">
                    <i class="fas fa-exclamation-triangle"></i>
                    <span class="d-none d-md-inline">Emergency</span>
                </button>

                <!-- Logout -->
                <button class="btn btn-outline-secondary" onclick="window.location.href='index-demo.html'">
                    <i class="fas fa-sign-out-alt"></i>
                    <span class="d-none d-md-inline">Logout</span>
                </button>
            </div>
        </div>
    </nav>

    <!-- Welcome Section -->
    <div class="container-fluid mt-3">
        <div class="row">
            <div class="col-12">
                <div class="welcome-card ubuntu-gradient">
                    <div class="row align-items-center">
                        <div class="col-md-8">
                            <h2 class="welcome-title">Sawubona, Thabo Mthembu!</h2>
                            <p class="welcome-subtitle">Your health journey continues with Ubuntu spirit - we're here for you</p>
                        </div>
                        <div class="col-md-4 text-end">
                            <div class="ubuntu-symbol">
                                <i class="fas fa-users"></i>
                                <span>Ubuntu</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Dashboard Content -->
    <div class="container-fluid">
        <div class="row">
            <!-- Left Sidebar - Chat Monitor -->
            <div class="col-lg-3">
                <div class="card chat-monitor-card">
                    <div class="card-header bg-ubuntu">
                        <h5 class="card-title text-white mb-0">
                            <i class="fas fa-robot"></i>
                            Chat Monitor
                        </h5>
                    </div>
                    <div class="card-body p-0">
                        <div class="chat-container" id="chat-container">
                            <div class="chat-message bot-message">
                                <div class="message-content">
                                    <p>Sawubona! I'm your Ubuntu Health Assistant. How are you feeling today?</p>
                                    <small class="text-muted">Just now</small>
                                </div>
                            </div>
                            <div class="chat-message user-message">
                                <div class="message-content">
                                    <p>I'm feeling good today, thank you!</p>
                                    <small class="text-muted">2 min ago</small>
                                </div>
                            </div>
                            <div class="chat-message bot-message">
                                <div class="message-content">
                                    <p>That's wonderful to hear! Remember to take your medication at 8 AM. Have you taken your Amlodipine today?</p>
                                    <small class="text-muted">1 min ago</small>
                                </div>
                            </div>
                        </div>
                        <div class="chat-input-container">
                            <div class="input-group">
                                <input type="text" class="form-control" id="chat-input" placeholder="Type your message...">
                                <button class="btn btn-ubuntu" type="button" id="send-chat">
                                    <i class="fas fa-paper-plane"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Main Content Area -->
            <div class="col-lg-9">
                <!-- Quick Actions Row -->
                <div class="row mb-4">
                    <div class="col-12">
                        <h4 class="section-title">Quick Actions</h4>
                    </div>
                    <div class="col-md-3 mb-3">
                        <div class="quick-action-card appointments-card">
                            <div class="card-icon">
                                <i class="fas fa-calendar-check"></i>
                            </div>
                            <h6>Next Appointment</h6>
                            <p class="appointment-time">Jan 15, 10:00 AM</p>
                            <small class="text-muted">Dr. Mandela Clinic</small>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <div class="quick-action-card medications-card">
                            <div class="card-icon">
                                <i class="fas fa-pills"></i>
                            </div>
                            <h6>Medications</h6>
                            <p class="medication-count">3 Active</p>
                            <small class="text-muted">View all medications</small>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <div class="quick-action-card scanner-card" data-bs-toggle="modal" data-bs-target="#scannerModal">
                            <div class="card-icon">
                                <i class="fas fa-qrcode"></i>
                            </div>
                            <h6>Barcode Scanner</h6>
                            <p>Scan Medication</p>
                            <small class="text-muted">Check medication info</small>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <div class="quick-action-card tips-card" onclick="showDailyTip()">
                            <div class="card-icon">
                                <i class="fas fa-lightbulb"></i>
                            </div>
                            <h6>Daily Health Tips</h6>
                            <p>Ubuntu Wellness</p>
                            <small class="text-muted">Today's tip available</small>
                        </div>
                    </div>
                </div>

                <!-- Health Monitoring Section -->
                <div class="row mb-4">
                    <div class="col-12">
                        <h4 class="section-title">Health Monitoring</h4>
                    </div>
                    <div class="col-md-6 mb-3">
                        <div class="card health-card hypertension-card">
                            <div class="card-body">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        <h6 class="card-title">
                                            <i class="fas fa-heartbeat text-danger"></i>
                                            Hypertension
                                        </h6>
                                        <p class="health-reading">120/80 mmHg</p>
                                        <small class="text-success">Normal Range</small>
                                    </div>
                                    <div class="health-chart">
                                        <canvas id="bpChart" width="100" height="60"></canvas>
                                    </div>
                                </div>
                                <button class="btn btn-sm btn-outline-primary mt-2">
                                    <i class="fas fa-upload"></i>
                                    Upload Reading
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6 mb-3">
                        <div class="card health-card diabetes-card">
                            <div class="card-body">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        <h6 class="card-title">
                                            <i class="fas fa-tint text-primary"></i>
                                            Diabetes
                                        </h6>
                                        <p class="health-reading">5.8 mmol/L</p>
                                        <small class="text-warning">Monitor Closely</small>
                                    </div>
                                    <div class="health-chart">
                                        <canvas id="glucoseChart" width="100" height="60"></canvas>
                                    </div>
                                </div>
                                <button class="btn btn-sm btn-outline-primary mt-2">
                                    <i class="fas fa-upload"></i>
                                    Upload Reading
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Detailed Information Cards -->
                <div class="row">
                    <div class="col-md-6 mb-4">
                        <div class="card info-card">
                            <div class="card-header bg-light">
                                <h6 class="mb-0">
                                    <i class="fas fa-pills text-success"></i>
                                    Current Medications
                                </h6>
                            </div>
                            <div class="card-body">
                                <div class="medication-item">
                                    <div class="d-flex justify-content-between">
                                        <span class="medication-name">Amlodipine 5mg</span>
                                        <span class="badge bg-success">Active</span>
                                    </div>
                                    <small class="text-muted">Once daily - 6 months</small>
                                </div>
                                <div class="medication-item">
                                    <div class="d-flex justify-content-between">
                                        <span class="medication-name">Metformin 500mg</span>
                                        <span class="badge bg-success">Active</span>
                                    </div>
                                    <small class="text-muted">Twice daily - 1 year</small>
                                </div>
                                <div class="medication-item">
                                    <div class="d-flex justify-content-between">
                                        <span class="medication-name">Vitamin D3</span>
                                        <span class="badge bg-success">Active</span>
                                    </div>
                                    <small class="text-muted">Once daily - 3 months</small>
                                </div>
                                <button class="btn btn-sm btn-ubuntu mt-2 w-100">View All Medications</button>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-6 mb-4">
                        <div class="card info-card">
                            <div class="card-header bg-light">
                                <h6 class="mb-0">
                                    <i class="fas fa-calendar-alt text-primary"></i>
                                    Upcoming Appointments
                                </h6>
                            </div>
                            <div class="card-body">
                                <div class="appointment-item">
                                    <div class="appointment-date">
                                        <span class="date">15</span>
                                        <span class="month">Jan</span>
                                    </div>
                                    <div class="appointment-details">
                                        <h6>Dr. Sarah Mthembu</h6>
                                        <p class="mb-1">General Checkup</p>
                                        <small class="text-muted">10:00 AM - Mandela Clinic</small>
                                    </div>
                                </div>
                                <div class="appointment-item">
                                    <div class="appointment-date">
                                        <span class="date">22</span>
                                        <span class="month">Jan</span>
                                    </div>
                                    <div class="appointment-details">
                                        <h6>Dr. John Radebe</h6>
                                        <p class="mb-1">Diabetes Follow-up</p>
                                        <small class="text-muted">2:30 PM - Ubuntu Health Center</small>
                                    </div>
                                </div>
                                <button class="btn btn-sm btn-ubuntu mt-2 w-100">View All Appointments</button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Medical Report Summary -->
                <div class="row">
                    <div class="col-12">
                        <div class="card info-card">
                            <div class="card-header bg-light">
                                <h6 class="mb-0">
                                    <i class="fas fa-file-medical text-info"></i>
                                    Medical Report Summary
                                </h6>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-3">
                                        <div class="report-stat">
                                            <h4 class="text-success">Good</h4>
                                            <small>Overall Health</small>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="report-stat">
                                            <h4 class="text-primary">2</h4>
                                            <small>Conditions Monitored</small>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="report-stat">
                                            <h4 class="text-warning">1</h4>
                                            <small>Pending Tests</small>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="report-stat">
                                            <h4 class="text-info">95%</h4>
                                            <small>Medication Adherence</small>
                                        </div>
                                    </div>
                                </div>
                                <button class="btn btn-ubuntu mt-3">
                                    <i class="fas fa-download"></i>
                                    Download Full Report
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Emergency Modal -->
    <div class="modal fade" id="emergencyModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header bg-danger text-white">
                    <h5 class="modal-title">
                        <i class="fas fa-exclamation-triangle"></i>
                        Emergency Support
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="emergency-form">
                        <div class="mb-3">
                            <label for="emergency-name" class="form-label">Full Name</label>
                            <input type="text" class="form-control" id="emergency-name" required>
                        </div>
                        <div class="mb-3">
                            <label for="emergency-surname" class="form-label">Surname</label>
                            <input type="text" class="form-control" id="emergency-surname" required>
                        </div>
                        <div class="mb-3">
                            <label for="emergency-id" class="form-label">ID Number</label>
                            <input type="text" class="form-control" id="emergency-id" required>
                        </div>
                        <div class="mb-3">
                            <label for="emergency-type" class="form-label">Emergency Type</label>
                            <select class="form-select" id="emergency-type" required>
                                <option value="">Select emergency type</option>
                                <option value="chest-pain">Chest Pain</option>
                                <option value="breathing">Difficulty Breathing</option>
                                <option value="injury">Serious Injury</option>
                                <option value="allergic">Allergic Reaction</option>
                                <option value="other">Other</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label for="emergency-description" class="form-label">Description</label>
                            <textarea class="form-control" id="emergency-description" rows="3" required></textarea>
                        </div>
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle"></i>
                            This will notify the nearest clinic, your doctor, and any linked relatives immediately.
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-danger" id="submit-emergency">
                        <i class="fas fa-ambulance"></i>
                        Send Emergency Alert
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Barcode Scanner Modal -->
    <div class="modal fade" id="scannerModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="fas fa-qrcode"></i>
                        Medication Barcode Scanner
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="text-center">
                        <div class="scanner-area">
                            <i class="fas fa-camera fa-3x text-muted mb-3"></i>
                            <p>Position the medication barcode within the frame</p>
                            <button class="btn btn-ubuntu" id="start-scanner">
                                <i class="fas fa-camera"></i>
                                Start Camera
                            </button>
                        </div>
                        <div class="scanner-result mt-4" id="scanner-result" style="display: none;">
                            <div class="card">
                                <div class="card-body">
                                    <h6 class="card-title">Medication Information</h6>
                                    <div class="medication-info">
                                        <p><strong>Name:</strong> <span id="med-name">Amlodipine 5mg</span></p>
                                        <p><strong>Manufacturer:</strong> <span id="med-manufacturer">Pharma SA</span></p>
                                        <p><strong>Expiry Date:</strong> <span id="med-expiry">2025-12-31</span></p>
                                        <p><strong>Batch Number:</strong> <span id="med-batch">*********</span></p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script>
        // Dashboard JavaScript
        document.addEventListener('DOMContentLoaded', function() {

            // Language translations
            const translations = {
                'en': { 'how_are_you': 'Sawubona! How are you feeling today?' },
                'zu': { 'how_are_you': 'Sawubona! Uzizwa kanjani namuhla?' },
                'xh': { 'how_are_you': 'Molo! Uziva njani namhlanje?' },
                'af': { 'how_are_you': 'Hallo! Hoe voel jy vandag?' }
            };

            let currentLanguage = 'en';

            // Initialize dashboard
            initializeDashboard();
            setupEventListeners();
            initializeHealthCharts();

            function initializeDashboard() {
                // Add welcome animation
                setTimeout(() => {
                    document.querySelector('.welcome-card').classList.add('fade-in');
                }, 300);

                // Animate quick action cards
                document.querySelectorAll('.quick-action-card').forEach((card, index) => {
                    setTimeout(() => {
                        card.classList.add('fade-in');
                    }, 500 + (index * 100));
                });
            }

            function setupEventListeners() {
                // Language selection
                document.querySelectorAll('[data-lang]').forEach(item => {
                    item.addEventListener('click', function(e) {
                        e.preventDefault();
                        changeLanguage(this.dataset.lang);
                    });
                });

                // Chat functionality
                const chatInput = document.getElementById('chat-input');
                const sendButton = document.getElementById('send-chat');

                if (chatInput && sendButton) {
                    sendButton.addEventListener('click', sendChatMessage);
                    chatInput.addEventListener('keypress', function(e) {
                        if (e.key === 'Enter') {
                            sendChatMessage();
                        }
                    });
                }

                // Emergency form
                const submitEmergency = document.getElementById('submit-emergency');
                if (submitEmergency) {
                    submitEmergency.addEventListener('click', handleEmergencySubmit);
                }

                // Voice call functionality
                const voiceCallBtn = document.getElementById('voice-call-btn');
                if (voiceCallBtn) {
                    voiceCallBtn.addEventListener('click', initiateVoiceCall);
                }

                // Barcode scanner
                const startScanner = document.getElementById('start-scanner');
                if (startScanner) {
                    startScanner.addEventListener('click', startBarcodeScanner);
                }
            }

            function changeLanguage(langCode) {
                currentLanguage = langCode;
                document.getElementById('current-language').textContent = getLanguageName(langCode);

                // Update chat bot message
                const trans = translations[langCode] || translations['en'];
                const botMessage = document.querySelector('.bot-message .message-content p');
                if (botMessage) {
                    botMessage.textContent = trans.how_are_you;
                }

                showNotification(`Language changed to ${getLanguageName(langCode)}`, 'success');
            }

            function getLanguageName(code) {
                const languages = {
                    'en': 'English', 'zu': 'isiZulu', 'xh': 'isiXhosa', 'af': 'Afrikaans',
                    'st': 'Sesotho', 'tn': 'Setswana', 've': 'Tshivenda', 'ts': 'Xitsonga',
                    'ss': 'siSwati', 'nr': 'isiNdebele', 'nso': 'Sepedi'
                };
                return languages[code] || 'English';
            }

            function sendChatMessage() {
                const input = document.getElementById('chat-input');
                const message = input.value.trim();

                if (message) {
                    addChatMessage(message, 'user');
                    input.value = '';

                    setTimeout(() => {
                        handleBotResponse(message);
                    }, 1000);
                }
            }

            function addChatMessage(message, sender) {
                const chatContainer = document.getElementById('chat-container');
                const messageDiv = document.createElement('div');
                messageDiv.className = `chat-message ${sender}-message`;

                messageDiv.innerHTML = `
                    <div class="message-content">
                        <p>${message}</p>
                        <small class="text-muted">${new Date().toLocaleTimeString()}</small>
                    </div>
                `;

                chatContainer.appendChild(messageDiv);
                chatContainer.scrollTop = chatContainer.scrollHeight;
            }

            function handleBotResponse(userMessage) {
                let response = "Thank you for sharing. I'm here to help you with your health journey.";

                const seriousKeywords = ['pain', 'chest', 'breathing', 'dizzy', 'emergency', 'help', 'urgent'];
                const medicationKeywords = ['medication', 'pills', 'medicine', 'dose', 'tablet'];

                if (seriousKeywords.some(keyword => userMessage.toLowerCase().includes(keyword))) {
                    response = "I'm concerned about what you've shared. I'm flagging this as a serious case and notifying the nearest clinic. Please stay calm, help is on the way.";
                    showNotification('Emergency services have been notified', 'warning');
                } else if (medicationKeywords.some(keyword => userMessage.toLowerCase().includes(keyword))) {
                    response = "I can help you with medication reminders. Have you taken your prescribed medication today?";
                } else if (userMessage.toLowerCase().includes('better')) {
                    response = "I'm so glad to hear you're feeling better! Your recovery brings joy to our Ubuntu community. Keep taking care of yourself.";
                }

                addChatMessage(response, 'bot');
            }

            function handleEmergencySubmit() {
                showNotification('Emergency alert sent! Help is on the way.', 'success');
                const modal = bootstrap.Modal.getInstance(document.getElementById('emergencyModal'));
                modal.hide();
                document.getElementById('emergency-form').reset();
            }

            function initiateVoiceCall() {
                showNotification('Connecting to Ubuntu Health Assistant...', 'info');
                setTimeout(() => {
                    showNotification('Voice call connected! Speak now.', 'success');
                }, 2000);
            }

            function startBarcodeScanner() {
                showNotification('Camera starting...', 'info');
                setTimeout(() => {
                    document.getElementById('scanner-result').style.display = 'block';
                    showNotification('Medication information loaded!', 'success');
                }, 3000);
            }

            function initializeHealthCharts() {
                // Blood pressure chart
                const bpCtx = document.getElementById('bpChart');
                if (bpCtx) {
                    new Chart(bpCtx, {
                        type: 'line',
                        data: {
                            labels: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri'],
                            datasets: [{
                                data: [120, 118, 122, 119, 120],
                                borderColor: '#DE3831',
                                backgroundColor: 'rgba(222, 56, 49, 0.1)',
                                tension: 0.4
                            }]
                        },
                        options: {
                            responsive: true,
                            plugins: { legend: { display: false } },
                            scales: { x: { display: false }, y: { display: false } }
                        }
                    });
                }

                // Glucose chart
                const glucoseCtx = document.getElementById('glucoseChart');
                if (glucoseCtx) {
                    new Chart(glucoseCtx, {
                        type: 'line',
                        data: {
                            labels: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri'],
                            datasets: [{
                                data: [5.8, 6.1, 5.9, 6.0, 5.8],
                                borderColor: '#002395',
                                backgroundColor: 'rgba(0, 35, 149, 0.1)',
                                tension: 0.4
                            }]
                        },
                        options: {
                            responsive: true,
                            plugins: { legend: { display: false } },
                            scales: { x: { display: false }, y: { display: false } }
                        }
                    });
                }
            }

            function showNotification(message, type = 'info') {
                const notification = document.createElement('div');
                notification.className = `alert alert-${type} notification-toast`;
                notification.innerHTML = `
                    <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'warning' ? 'exclamation-triangle' : 'info-circle'}"></i>
                    ${message}
                `;

                document.body.appendChild(notification);

                setTimeout(() => {
                    notification.style.animation = 'slideOut 0.3s ease-in';
                    setTimeout(() => notification.remove(), 300);
                }, 5000);
            }

            // Make showDailyTip global
            window.showDailyTip = function() {
                const tips = [
                    "Ubuntu teaches us that our well-being is connected to others. Share a healthy meal with family today.",
                    "Take a walk in nature - South Africa's beautiful landscapes can heal both body and soul.",
                    "Remember: 'I am because we are' - your health journey affects your whole community.",
                    "Practice gratitude today - it's good for your mental health and Ubuntu spirit.",
                    "Stay hydrated with clean water - a basic right for all South Africans."
                ];

                const randomTip = tips[Math.floor(Math.random() * tips.length)];
                showNotification(randomTip, 'info');
            };

            console.log('🇿🇦 Ubuntu Healthcare Dashboard Loaded - "I am because we are" 🇿🇦');
        });

        // Add notification animations
        const notificationStyle = document.createElement('style');
        notificationStyle.textContent = `
            @keyframes slideOut {
                from { transform: translateX(0); opacity: 1; }
                to { transform: translateX(100%); opacity: 0; }
            }
        `;
        document.head.appendChild(notificationStyle);
    </script>
</body>
</html>
