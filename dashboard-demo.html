<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Patient Dashboard - Ubuntu Healthcare SA</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            --sa-green: #00B04F;
            --sa-gold: #FFB612;
            --sa-orange: #FF8C42;
            --dark-bg: #1a1a1a;
            --card-bg: #2a2a2a;
            --glass-bg: rgba(255, 255, 255, 0.1);
            --ubuntu-gradient: linear-gradient(135deg, var(--sa-green) 0%, #00D4AA 100%);
            --orange-gradient: linear-gradient(135deg, var(--sa-orange) 0%, #FF6B35 100%);
            --dark-gradient: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
        }

        body {
            font-family: 'Inter', 'Segoe UI', sans-serif;
            background: var(--dark-bg);
            color: white;
            min-height: 100vh;
        }

        /* Animated Background */
        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background:
                radial-gradient(circle at 20% 80%, rgba(0, 176, 79, 0.2) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(255, 140, 66, 0.2) 0%, transparent 50%),
                radial-gradient(circle at 40% 40%, rgba(255, 182, 18, 0.1) 0%, transparent 50%);
            animation: backgroundShift 20s ease-in-out infinite;
            z-index: -1;
        }

        @keyframes backgroundShift {
            0%, 100% { transform: scale(1) rotate(0deg); }
            50% { transform: scale(1.1) rotate(5deg); }
        }

        .ubuntu-navbar {
            background: var(--glass-bg);
            backdrop-filter: blur(20px);
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            padding: 1rem 0;
        }

        .navbar-brand {
            font-size: 1.5rem;
            font-weight: 700;
            color: white !important;
        }

        .welcome-card {
            background: var(--glass-bg);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            color: white;
            padding: 2.5rem;
            border-radius: 25px;
            margin-bottom: 2rem;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
            position: relative;
            overflow: hidden;
        }

        .welcome-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: var(--ubuntu-gradient);
            opacity: 0.1;
            z-index: -1;
        }

        .welcome-title {
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
        }

        .ubuntu-symbol {
            text-align: center;
            background: rgba(255, 255, 255, 0.2);
            padding: 1rem;
            border-radius: 50%;
            width: 80px;
            height: 80px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            backdrop-filter: blur(10px);
        }

        .chat-monitor-card {
            height: 500px;
            display: flex;
            flex-direction: column;
            background: var(--glass-bg);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 25px;
        }

        .bg-ubuntu {
            background: var(--orange-gradient) !important;
            border-radius: 25px 25px 0 0;
        }

        .chat-container {
            flex: 1;
            overflow-y: auto;
            padding: 1rem;
            background: rgba(0, 0, 0, 0.2);
        }

        .chat-message {
            margin-bottom: 1rem;
        }

        .bot-message .message-content {
            background: var(--sa-green);
            color: white;
            padding: 0.8rem 1rem;
            border-radius: 15px 15px 15px 5px;
            max-width: 80%;
        }

        .user-message .message-content {
            background: var(--sa-gold);
            color: #2c3e50;
            padding: 0.8rem 1rem;
            border-radius: 15px 15px 5px 15px;
            max-width: 80%;
            margin-left: auto;
        }

        .chat-input-container {
            padding: 1rem;
            background: white;
            border-top: 1px solid #dee2e6;
        }

        .btn-ubuntu {
            background: var(--sa-green);
            border: none;
            color: white;
        }

        .btn-ubuntu:hover {
            background: #005a37;
            color: white;
        }

        .section-title {
            font-size: 1.5rem;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 1rem;
            border-left: 4px solid var(--sa-green);
            padding-left: 1rem;
        }

        .quick-action-card {
            background: var(--glass-bg);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            padding: 2rem 1.5rem;
            border-radius: 20px;
            text-align: center;
            height: 100%;
            transition: all 0.4s ease;
            cursor: pointer;
            position: relative;
            overflow: hidden;
        }

        .quick-action-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
            transition: left 0.5s;
        }

        .quick-action-card:hover::before {
            left: 100%;
        }

        .quick-action-card:hover {
            transform: translateY(-10px) scale(1.02);
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.3);
            border-color: var(--sa-orange);
        }

        .appointments-card { border-top: 4px solid var(--sa-green); }
        .medications-card { border-top: 4px solid var(--sa-gold); }
        .scanner-card { border-top: 4px solid var(--sa-blue); }
        .tips-card { border-top: 4px solid var(--sa-red); }

        .card-icon {
            width: 60px;
            height: 60px;
            margin: 0 auto 1rem;
            background: var(--ubuntu-gradient);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.5rem;
        }

        .medications-card .card-icon {
            background: var(--gold-gradient);
            color: #2c3e50;
        }

        .scanner-card .card-icon {
            background: linear-gradient(135deg, var(--sa-blue) 0%, #1565C0 100%);
        }

        .tips-card .card-icon {
            background: linear-gradient(135deg, var(--sa-red) 0%, #C62828 100%);
        }

        .health-card {
            border: none;
            border-radius: 12px;
            box-shadow: 0 3px 15px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
        }

        .health-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }

        .hypertension-card { border-left: 5px solid var(--sa-red); }
        .diabetes-card { border-left: 5px solid var(--sa-blue); }

        .health-reading {
            font-size: 1.5rem;
            font-weight: 700;
            margin: 0.5rem 0;
            color: #2c3e50;
        }

        .info-card {
            border: none;
            border-radius: 12px;
            box-shadow: 0 3px 15px rgba(0,0,0,0.1);
            margin-bottom: 1.5rem;
        }

        .medication-item {
            padding: 0.8rem 0;
            border-bottom: 1px solid #f1f3f4;
        }

        .medication-item:last-child { border-bottom: none; }

        .appointment-item {
            display: flex;
            align-items: center;
            padding: 1rem 0;
            border-bottom: 1px solid #f1f3f4;
        }

        .appointment-item:last-child { border-bottom: none; }

        .appointment-date {
            background: var(--ubuntu-gradient);
            color: white;
            padding: 0.8rem;
            border-radius: 10px;
            text-align: center;
            margin-right: 1rem;
            min-width: 60px;
        }

        .appointment-date .date {
            font-size: 1.2rem;
            font-weight: 700;
            display: block;
        }

        .appointment-date .month {
            font-size: 0.8rem;
            opacity: 0.9;
        }

        .report-stat {
            text-align: center;
            padding: 1rem;
            background: rgba(0, 119, 73, 0.05);
            border-radius: 10px;
            margin-bottom: 1rem;
        }

        .report-stat h4 {
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: 0.3rem;
        }

        .fade-in {
            animation: fadeInUp 0.6s ease-out forwards;
        }

        @keyframes fadeInUp {
            from { opacity: 0; transform: translateY(30px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .notification-toast {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 9999;
            min-width: 300px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
            border: none;
            border-radius: 10px;
        }

        /* Advanced Animations & Effects */
        .floating-elements {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: -1;
        }

        .floating-icon {
            position: absolute;
            color: rgba(255, 140, 66, 0.1);
            animation: floatAround 15s infinite linear;
        }

        @keyframes floatAround {
            0% { transform: translateY(100vh) rotate(0deg); }
            100% { transform: translateY(-100px) rotate(360deg); }
        }

        .floating-icon:nth-child(1) { left: 10%; animation-delay: 0s; font-size: 2rem; }
        .floating-icon:nth-child(2) { left: 20%; animation-delay: 2s; font-size: 1.5rem; }
        .floating-icon:nth-child(3) { left: 30%; animation-delay: 4s; font-size: 2.5rem; }
        .floating-icon:nth-child(4) { left: 40%; animation-delay: 6s; font-size: 1.8rem; }
        .floating-icon:nth-child(5) { left: 50%; animation-delay: 8s; font-size: 2rem; }
        .floating-icon:nth-child(6) { left: 60%; animation-delay: 10s; font-size: 1.6rem; }
        .floating-icon:nth-child(7) { left: 70%; animation-delay: 12s; font-size: 2.2rem; }
        .floating-icon:nth-child(8) { left: 80%; animation-delay: 14s; font-size: 1.7rem; }
        .floating-icon:nth-child(9) { left: 90%; animation-delay: 16s; font-size: 1.9rem; }

        /* Improved Cards with Micro-interactions */
        .quick-action-card {
            background: var(--glass-bg);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            padding: 2rem 1.5rem;
            border-radius: 20px;
            text-align: center;
            height: 100%;
            transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            cursor: pointer;
            position: relative;
            overflow: hidden;
        }

        .quick-action-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.6s;
        }

        .quick-action-card:hover::before {
            left: 100%;
        }

        .quick-action-card:hover {
            transform: translateY(-15px) scale(1.05);
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.4);
            border-color: var(--sa-orange);
        }

        .quick-action-card:active {
            transform: translateY(-10px) scale(1.02);
        }

        /* Enhanced Card Icons with Rotation */
        .card-icon {
            width: 70px;
            height: 70px;
            margin: 0 auto 1.5rem;
            background: var(--orange-gradient);
            border-radius: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.8rem;
            transition: all 0.4s ease;
            position: relative;
        }

        .card-icon::after {
            content: '';
            position: absolute;
            top: -2px;
            left: -2px;
            right: -2px;
            bottom: -2px;
            background: var(--orange-gradient);
            border-radius: 22px;
            z-index: -1;
            opacity: 0;
            transition: opacity 0.3s;
        }

        .quick-action-card:hover .card-icon {
            transform: rotateY(180deg) scale(1.1);
        }

        .quick-action-card:hover .card-icon::after {
            opacity: 0.3;
            animation: iconGlow 1s ease-in-out infinite alternate;
        }

        @keyframes iconGlow {
            0% { box-shadow: 0 0 20px rgba(255, 140, 66, 0.5); }
            100% { box-shadow: 0 0 40px rgba(255, 140, 66, 0.8); }
        }

        /* Improved Health Cards with Progress Rings */
        .health-card {
            background: var(--glass-bg);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            transition: all 0.4s ease;
            position: relative;
            overflow: hidden;
        }

        .health-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: var(--orange-gradient);
            transform: scaleX(0);
            transition: transform 0.3s ease;
        }

        .health-card:hover::before {
            transform: scaleX(1);
        }

        .health-card:hover {
            transform: translateY(-8px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
        }

        /* Animated Progress Rings */
        .progress-ring {
            width: 60px;
            height: 60px;
            position: relative;
        }

        .progress-ring svg {
            width: 100%;
            height: 100%;
            transform: rotate(-90deg);
        }

        .progress-ring circle {
            fill: none;
            stroke-width: 4;
            stroke-linecap: round;
        }

        .progress-ring .bg {
            stroke: rgba(255, 255, 255, 0.1);
        }

        .progress-ring .progress {
            stroke: var(--sa-orange);
            stroke-dasharray: 157;
            stroke-dashoffset: 157;
            animation: progressFill 2s ease-out forwards;
        }

        @keyframes progressFill {
            to { stroke-dashoffset: 47; }
        }

        /* Enhanced Chat with Typing Indicator */
        .typing-indicator {
            display: none;
            padding: 0.8rem 1rem;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px 15px 15px 5px;
            max-width: 80px;
            margin-bottom: 1rem;
        }

        .typing-indicator span {
            height: 8px;
            width: 8px;
            background: var(--sa-orange);
            border-radius: 50%;
            display: inline-block;
            margin-right: 5px;
            animation: typingDots 1.4s infinite ease-in-out;
        }

        .typing-indicator span:nth-child(2) { animation-delay: 0.2s; }
        .typing-indicator span:nth-child(3) { animation-delay: 0.4s; }

        @keyframes typingDots {
            0%, 60%, 100% { transform: translateY(0); opacity: 0.5; }
            30% { transform: translateY(-10px); opacity: 1; }
        }

        /* Notification System */
        .notification-system {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1000;
        }

        .notification {
            background: var(--glass-bg);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 15px;
            padding: 1rem 1.5rem;
            margin-bottom: 1rem;
            color: white;
            min-width: 300px;
            transform: translateX(400px);
            animation: slideInNotification 0.5s ease-out forwards;
        }

        @keyframes slideInNotification {
            to { transform: translateX(0); }
        }

        .notification.success { border-left: 4px solid var(--sa-green); }
        .notification.warning { border-left: 4px solid var(--sa-gold); }
        .notification.info { border-left: 4px solid var(--sa-orange); }

        /* Improved Responsive Design */
        @media (max-width: 768px) {
            .welcome-title { font-size: 1.5rem; }
            .chat-monitor-card { height: 400px; margin-bottom: 2rem; }
            .quick-action-card { margin-bottom: 1rem; }
            .floating-icon { display: none; }
            .card-icon { width: 60px; height: 60px; font-size: 1.5rem; }
        }

        /* Language Dropdown Styling - High Priority Z-Index */
        .language-selector {
            color: var(--text-secondary) !important;
            font-weight: 500;
            padding: 0.5rem 1rem !important;
            border-radius: 8px;
            transition: all 0.2s ease;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .language-selector:hover {
            background: var(--card-bg);
            color: var(--text-primary) !important;
        }

        .language-selector i {
            color: var(--sa-green) !important;
        }

        .dropdown-menu {
            background: rgba(45, 55, 72, 0.95) !important;
            border: 1px solid rgba(255, 255, 255, 0.2) !important;
            border-radius: 12px !important;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.5) !important;
            backdrop-filter: blur(20px) !important;
            padding: 0.5rem 0 !important;
            min-width: 200px !important;
            margin-top: 0.5rem !important;
            z-index: 99999 !important;
            position: absolute !important;
        }

        .light-theme .dropdown-menu {
            background: rgba(255, 255, 255, 0.98) !important;
            border: 1px solid rgba(0, 0, 0, 0.1) !important;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15) !important;
        }

        .dropdown-item {
            color: rgba(255, 255, 255, 0.9) !important;
            padding: 0.75rem 1rem !important;
            font-size: 0.9rem !important;
            border-radius: 8px !important;
            margin: 0 0.5rem !important;
            transition: all 0.2s ease !important;
            background: transparent !important;
        }

        .dropdown-item:hover {
            background: var(--sa-green) !important;
            color: white !important;
            transform: translateX(4px) !important;
        }

        .dropdown-item:focus {
            background: var(--sa-green) !important;
            color: white !important;
            outline: none !important;
        }

        .light-theme .dropdown-item {
            color: #1F2937 !important;
        }

        .light-theme .dropdown-item:hover {
            background: var(--sa-green) !important;
            color: white !important;
        }

        .light-theme .dropdown-item:focus {
            background: var(--sa-green) !important;
            color: white !important;
        }

        /* Ensure navbar has proper z-index */
        .ubuntu-navbar {
            z-index: 1040 !important;
            position: relative !important;
        }

        .nav-item.dropdown {
            z-index: 99999 !important;
            position: relative !important;
        }

        /* Dark Mode Toggle */
        .theme-toggle {
            position: fixed;
            bottom: 30px;
            right: 30px;
            width: 60px;
            height: 60px;
            background: var(--orange-gradient);
            border: none;
            border-radius: 50%;
            color: white;
            font-size: 1.5rem;
            cursor: pointer;
            box-shadow: 0 10px 25px rgba(255, 140, 66, 0.4);
            transition: all 0.3s ease;
            z-index: 1000;
        }

        .theme-toggle:hover {
            transform: scale(1.1) rotate(180deg);
            box-shadow: 0 15px 35px rgba(255, 140, 66, 0.6);
        }
    </style>
</head>
<body>
    <!-- Floating Background Elements -->
    <div class="floating-elements">
        <i class="floating-icon fas fa-heart-pulse"></i>
        <i class="floating-icon fas fa-pills"></i>
        <i class="floating-icon fas fa-stethoscope"></i>
        <i class="floating-icon fas fa-user-md"></i>
        <i class="floating-icon fas fa-hospital"></i>
        <i class="floating-icon fas fa-heartbeat"></i>
        <i class="floating-icon fas fa-medical-kit"></i>
        <i class="floating-icon fas fa-ambulance"></i>
        <i class="floating-icon fas fa-syringe"></i>
    </div>

    <!-- Notification System -->
    <div class="notification-system" id="notification-system"></div>
    <!-- Top Navigation Bar -->
    <nav class="navbar navbar-expand-lg ubuntu-navbar">
        <div class="container-fluid">
            <div class="navbar-brand">
                <i class="fas fa-heart-pulse text-danger"></i>
                <span class="brand-text">Ubuntu Healthcare</span>
            </div>

            <!-- Language Selection -->
            <div class="navbar-nav me-auto">
                <div class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle language-selector" href="#" role="button" data-bs-toggle="dropdown" style="color: var(--sa-green); font-weight: 500;">
                        <i class="fas fa-globe"></i>
                        <span id="current-language">English</span>
                    </a>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item" href="#" onclick="changeLanguage('English', '🇬🇧')">🇬🇧 English</a></li>
                        <li><a class="dropdown-item" href="#" onclick="changeLanguage('isiZulu', '🇿🇦')">🇿🇦 isiZulu</a></li>
                        <li><a class="dropdown-item" href="#" onclick="changeLanguage('isiXhosa', '🇿🇦')">🇿🇦 isiXhosa</a></li>
                        <li><a class="dropdown-item" href="#" onclick="changeLanguage('Afrikaans', '🇿🇦')">🇿🇦 Afrikaans</a></li>
                        <li><a class="dropdown-item" href="#" onclick="changeLanguage('Sesotho', '🇿🇦')">🇿🇦 Sesotho</a></li>
                        <li><a class="dropdown-item" href="#" onclick="changeLanguage('Setswana', '🇿🇦')">🇿🇦 Setswana</a></li>
                        <li><a class="dropdown-item" href="#" onclick="changeLanguage('Tshivenda', '🇿🇦')">🇿🇦 Tshivenda</a></li>
                        <li><a class="dropdown-item" href="#" onclick="changeLanguage('Xitsonga', '🇿🇦')">🇿🇦 Xitsonga</a></li>
                        <li><a class="dropdown-item" href="#" onclick="changeLanguage('siSwati', '🇿🇦')">🇿🇦 siSwati</a></li>
                        <li><a class="dropdown-item" href="#" onclick="changeLanguage('isiNdebele', '🇿🇦')">🇿🇦 isiNdebele</a></li>
                        <li><a class="dropdown-item" href="#" onclick="changeLanguage('Sepedi', '🇿🇦')">🇿🇦 Sepedi</a></li>
                    </ul>
                </div>
            </div>

            <!-- Right side navigation -->
            <div class="navbar-nav">
                <!-- Voice Call -->
                <button class="btn btn-outline-success me-2" id="voice-call-btn">
                    <i class="fas fa-phone"></i>
                    <span class="d-none d-md-inline">Voice Call</span>
                </button>

                <!-- Emergency Support -->
                <button class="btn btn-danger me-2" id="emergency-btn" data-bs-toggle="modal" data-bs-target="#emergencyModal">
                    <i class="fas fa-exclamation-triangle"></i>
                    <span class="d-none d-md-inline">Emergency</span>
                </button>

                <!-- Logout -->
                <button class="btn btn-outline-secondary" onclick="window.location.href='index-demo.html'">
                    <i class="fas fa-sign-out-alt"></i>
                    <span class="d-none d-md-inline">Logout</span>
                </button>
            </div>
        </div>
    </nav>

    <!-- Welcome Section -->
    <div class="container-fluid mt-3">
        <div class="row">
            <div class="col-12">
                <div class="welcome-card ubuntu-gradient">
                    <div class="row align-items-center">
                        <div class="col-md-8">
                            <h2 class="welcome-title">Sawubona, Thabo Mthembu!</h2>
                            <p class="welcome-subtitle">Your health journey continues with Ubuntu spirit - we're here for you</p>
                        </div>
                        <div class="col-md-4 text-end">
                            <div class="ubuntu-symbol">
                                <i class="fas fa-users"></i>
                                <span>Ubuntu</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Dashboard Content -->
    <div class="container-fluid">
        <div class="row">
            <!-- Left Sidebar - Chat Monitor -->
            <div class="col-lg-3">
                <div class="card chat-monitor-card">
                    <div class="card-header bg-ubuntu">
                        <h5 class="card-title text-white mb-0">
                            <i class="fas fa-robot"></i>
                            Chat Monitor
                        </h5>
                    </div>
                    <div class="card-body p-0">
                        <div class="chat-container" id="chat-container">
                            <div class="chat-message bot-message">
                                <div class="message-content">
                                    <p>Sawubona! I'm your Ubuntu Health Assistant. How are you feeling today?</p>
                                    <small class="text-muted">Just now</small>
                                </div>
                            </div>
                            <div class="chat-message user-message">
                                <div class="message-content">
                                    <p>I'm feeling good today, thank you!</p>
                                    <small class="text-muted">2 min ago</small>
                                </div>
                            </div>
                            <div class="chat-message bot-message">
                                <div class="message-content">
                                    <p>That's wonderful to hear! Remember to take your medication at 8 AM. Have you taken your Amlodipine today?</p>
                                    <small class="text-muted">1 min ago</small>
                                </div>
                            </div>
                            <div class="typing-indicator" id="typing-indicator">
                                <span></span>
                                <span></span>
                                <span></span>
                            </div>
                        </div>
                        <div class="chat-input-container">
                            <div class="input-group">
                                <input type="text" class="form-control" id="chat-input" placeholder="Type your message...">
                                <button class="btn btn-ubuntu" type="button" id="send-chat">
                                    <i class="fas fa-paper-plane"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Main Content Area -->
            <div class="col-lg-9">
                <!-- Quick Actions Row -->
                <div class="row mb-4">
                    <div class="col-12">
                        <h4 class="section-title">Quick Actions</h4>
                    </div>
                    <div class="col-md-3 mb-3">
                        <div class="quick-action-card appointments-card">
                            <div class="card-icon">
                                <i class="fas fa-calendar-check"></i>
                            </div>
                            <h6>Next Appointment</h6>
                            <p class="appointment-time">Jan 15, 10:00 AM</p>
                            <small class="text-muted">Dr. Mandela Clinic</small>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <div class="quick-action-card medications-card">
                            <div class="card-icon">
                                <i class="fas fa-pills"></i>
                            </div>
                            <h6>Medications</h6>
                            <p class="medication-count">3 Active</p>
                            <small class="text-muted">View all medications</small>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <div class="quick-action-card scanner-card" data-bs-toggle="modal" data-bs-target="#scannerModal">
                            <div class="card-icon">
                                <i class="fas fa-qrcode"></i>
                            </div>
                            <h6>Barcode Scanner</h6>
                            <p>Scan Medication</p>
                            <small class="text-muted">Check medication info</small>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <div class="quick-action-card tips-card" onclick="showDailyTip()">
                            <div class="card-icon">
                                <i class="fas fa-lightbulb"></i>
                            </div>
                            <h6>Daily Health Tips</h6>
                            <p>Ubuntu Wellness</p>
                            <small class="text-muted">Today's tip available</small>
                        </div>
                    </div>
                </div>

                <!-- Health Monitoring Section -->
                <div class="row mb-4">
                    <div class="col-12">
                        <h4 class="section-title">Health Monitoring</h4>
                    </div>
                    <div class="col-md-6 mb-3">
                        <div class="card health-card hypertension-card">
                            <div class="card-body">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        <h6 class="card-title">
                                            <i class="fas fa-heartbeat text-danger"></i>
                                            Hypertension
                                        </h6>
                                        <p class="health-reading">120/80 mmHg</p>
                                        <small class="text-success">Normal Range</small>
                                    </div>
                                    <div class="health-chart">
                                        <div class="progress-ring">
                                            <svg>
                                                <circle class="bg" cx="30" cy="30" r="25"></circle>
                                                <circle class="progress" cx="30" cy="30" r="25"></circle>
                                            </svg>
                                            <div style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); font-size: 0.8rem; font-weight: bold;">75%</div>
                                        </div>
                                    </div>
                                </div>
                                <button class="btn btn-sm btn-outline-primary mt-2">
                                    <i class="fas fa-upload"></i>
                                    Upload Reading
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6 mb-3">
                        <div class="card health-card diabetes-card">
                            <div class="card-body">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        <h6 class="card-title">
                                            <i class="fas fa-tint text-primary"></i>
                                            Diabetes
                                        </h6>
                                        <p class="health-reading">5.8 mmol/L</p>
                                        <small class="text-warning">Monitor Closely</small>
                                    </div>
                                    <div class="health-chart">
                                        <div class="progress-ring">
                                            <svg>
                                                <circle class="bg" cx="30" cy="30" r="25"></circle>
                                                <circle class="progress" cx="30" cy="30" r="25" style="stroke: var(--sa-green);"></circle>
                                            </svg>
                                            <div style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); font-size: 0.8rem; font-weight: bold;">82%</div>
                                        </div>
                                    </div>
                                </div>
                                <button class="btn btn-sm btn-outline-primary mt-2">
                                    <i class="fas fa-upload"></i>
                                    Upload Reading
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Detailed Information Cards -->
                <div class="row">
                    <div class="col-md-6 mb-4">
                        <div class="card info-card">
                            <div class="card-header bg-light">
                                <h6 class="mb-0">
                                    <i class="fas fa-pills text-success"></i>
                                    Current Medications
                                </h6>
                            </div>
                            <div class="card-body">
                                <div class="medication-item">
                                    <div class="d-flex justify-content-between">
                                        <span class="medication-name">Amlodipine 5mg</span>
                                        <span class="badge bg-success">Active</span>
                                    </div>
                                    <small class="text-muted">Once daily - 6 months</small>
                                </div>
                                <div class="medication-item">
                                    <div class="d-flex justify-content-between">
                                        <span class="medication-name">Metformin 500mg</span>
                                        <span class="badge bg-success">Active</span>
                                    </div>
                                    <small class="text-muted">Twice daily - 1 year</small>
                                </div>
                                <div class="medication-item">
                                    <div class="d-flex justify-content-between">
                                        <span class="medication-name">Vitamin D3</span>
                                        <span class="badge bg-success">Active</span>
                                    </div>
                                    <small class="text-muted">Once daily - 3 months</small>
                                </div>
                                <button class="btn btn-sm btn-ubuntu mt-2 w-100">View All Medications</button>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-6 mb-4">
                        <div class="card info-card">
                            <div class="card-header bg-light">
                                <h6 class="mb-0">
                                    <i class="fas fa-calendar-alt text-primary"></i>
                                    Upcoming Appointments
                                </h6>
                            </div>
                            <div class="card-body">
                                <div class="appointment-item">
                                    <div class="appointment-date">
                                        <span class="date">15</span>
                                        <span class="month">Jan</span>
                                    </div>
                                    <div class="appointment-details">
                                        <h6>Dr. Sarah Mthembu</h6>
                                        <p class="mb-1">General Checkup</p>
                                        <small class="text-muted">10:00 AM - Mandela Clinic</small>
                                    </div>
                                </div>
                                <div class="appointment-item">
                                    <div class="appointment-date">
                                        <span class="date">22</span>
                                        <span class="month">Jan</span>
                                    </div>
                                    <div class="appointment-details">
                                        <h6>Dr. John Radebe</h6>
                                        <p class="mb-1">Diabetes Follow-up</p>
                                        <small class="text-muted">2:30 PM - Ubuntu Health Center</small>
                                    </div>
                                </div>
                                <button class="btn btn-sm btn-ubuntu mt-2 w-100">View All Appointments</button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Medical Report Summary -->
                <div class="row">
                    <div class="col-12">
                        <div class="card info-card">
                            <div class="card-header bg-light">
                                <h6 class="mb-0">
                                    <i class="fas fa-file-medical text-info"></i>
                                    Medical Report Summary
                                </h6>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-3">
                                        <div class="report-stat">
                                            <h4 class="text-success">Good</h4>
                                            <small>Overall Health</small>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="report-stat">
                                            <h4 class="text-primary">2</h4>
                                            <small>Conditions Monitored</small>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="report-stat">
                                            <h4 class="text-warning">1</h4>
                                            <small>Pending Tests</small>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="report-stat">
                                            <h4 class="text-info">95%</h4>
                                            <small>Medication Adherence</small>
                                        </div>
                                    </div>
                                </div>
                                <button class="btn btn-ubuntu mt-3">
                                    <i class="fas fa-download"></i>
                                    Download Full Report
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Emergency Modal -->
    <div class="modal fade" id="emergencyModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header bg-danger text-white">
                    <h5 class="modal-title">
                        <i class="fas fa-exclamation-triangle"></i>
                        Emergency Support
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="emergency-form">
                        <div class="mb-3">
                            <label for="emergency-name" class="form-label">Full Name</label>
                            <input type="text" class="form-control" id="emergency-name" required>
                        </div>
                        <div class="mb-3">
                            <label for="emergency-surname" class="form-label">Surname</label>
                            <input type="text" class="form-control" id="emergency-surname" required>
                        </div>
                        <div class="mb-3">
                            <label for="emergency-id" class="form-label">ID Number</label>
                            <input type="text" class="form-control" id="emergency-id" required>
                        </div>
                        <div class="mb-3">
                            <label for="emergency-type" class="form-label">Emergency Type</label>
                            <select class="form-select" id="emergency-type" required>
                                <option value="">Select emergency type</option>
                                <option value="chest-pain">Chest Pain</option>
                                <option value="breathing">Difficulty Breathing</option>
                                <option value="injury">Serious Injury</option>
                                <option value="allergic">Allergic Reaction</option>
                                <option value="other">Other</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label for="emergency-description" class="form-label">Description</label>
                            <textarea class="form-control" id="emergency-description" rows="3" required></textarea>
                        </div>
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle"></i>
                            This will notify the nearest clinic, your doctor, and any linked relatives immediately.
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-danger" id="submit-emergency">
                        <i class="fas fa-ambulance"></i>
                        Send Emergency Alert
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Barcode Scanner Modal -->
    <div class="modal fade" id="scannerModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="fas fa-qrcode"></i>
                        Medication Barcode Scanner
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="text-center">
                        <div class="scanner-area">
                            <i class="fas fa-camera fa-3x text-muted mb-3"></i>
                            <p>Position the medication barcode within the frame</p>
                            <button class="btn btn-ubuntu" id="start-scanner">
                                <i class="fas fa-camera"></i>
                                Start Camera
                            </button>
                        </div>
                        <div class="scanner-result mt-4" id="scanner-result" style="display: none;">
                            <div class="card">
                                <div class="card-body">
                                    <h6 class="card-title">Medication Information</h6>
                                    <div class="medication-info">
                                        <p><strong>Name:</strong> <span id="med-name">Amlodipine 5mg</span></p>
                                        <p><strong>Manufacturer:</strong> <span id="med-manufacturer">Pharma SA</span></p>
                                        <p><strong>Expiry Date:</strong> <span id="med-expiry">2025-12-31</span></p>
                                        <p><strong>Batch Number:</strong> <span id="med-batch">*********</span></p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Theme Toggle Button -->
    <button class="theme-toggle" id="theme-toggle" title="Toggle Theme">
        <i class="fas fa-palette"></i>
    </button>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script>
        // Dashboard JavaScript
        document.addEventListener('DOMContentLoaded', function() {

            // Language translations
            const translations = {
                'en': { 'how_are_you': 'Sawubona! How are you feeling today?' },
                'zu': { 'how_are_you': 'Sawubona! Uzizwa kanjani namuhla?' },
                'xh': { 'how_are_you': 'Molo! Uziva njani namhlanje?' },
                'af': { 'how_are_you': 'Hallo! Hoe voel jy vandag?' }
            };

            let currentLanguage = 'en';

            // Initialize dashboard
            initializeDashboard();
            setupEventListeners();
            initializeHealthCharts();

            function initializeDashboard() {
                // Add welcome animation
                setTimeout(() => {
                    document.querySelector('.welcome-card').classList.add('fade-in');
                }, 300);

                // Animate quick action cards
                document.querySelectorAll('.quick-action-card').forEach((card, index) => {
                    setTimeout(() => {
                        card.classList.add('fade-in');
                    }, 500 + (index * 100));
                });
            }

            function setupEventListeners() {
                // Language selection
                document.querySelectorAll('[data-lang]').forEach(item => {
                    item.addEventListener('click', function(e) {
                        e.preventDefault();
                        changeLanguage(this.dataset.lang);
                    });
                });

                // Chat functionality
                const chatInput = document.getElementById('chat-input');
                const sendButton = document.getElementById('send-chat');

                if (chatInput && sendButton) {
                    sendButton.addEventListener('click', sendChatMessage);
                    chatInput.addEventListener('keypress', function(e) {
                        if (e.key === 'Enter') {
                            sendChatMessage();
                        }
                    });
                }

                // Emergency form
                const submitEmergency = document.getElementById('submit-emergency');
                if (submitEmergency) {
                    submitEmergency.addEventListener('click', handleEmergencySubmit);
                }

                // Voice call functionality
                const voiceCallBtn = document.getElementById('voice-call-btn');
                if (voiceCallBtn) {
                    voiceCallBtn.addEventListener('click', initiateVoiceCall);
                }

                // Barcode scanner
                const startScanner = document.getElementById('start-scanner');
                if (startScanner) {
                    startScanner.addEventListener('click', startBarcodeScanner);
                }
            }

            function changeLanguage(langCode) {
                currentLanguage = langCode;
                document.getElementById('current-language').textContent = getLanguageName(langCode);

                // Update chat bot message
                const trans = translations[langCode] || translations['en'];
                const botMessage = document.querySelector('.bot-message .message-content p');
                if (botMessage) {
                    botMessage.textContent = trans.how_are_you;
                }

                showNotification(`Language changed to ${getLanguageName(langCode)}`, 'success');
            }

            function getLanguageName(code) {
                const languages = {
                    'en': 'English', 'zu': 'isiZulu', 'xh': 'isiXhosa', 'af': 'Afrikaans',
                    'st': 'Sesotho', 'tn': 'Setswana', 've': 'Tshivenda', 'ts': 'Xitsonga',
                    'ss': 'siSwati', 'nr': 'isiNdebele', 'nso': 'Sepedi'
                };
                return languages[code] || 'English';
            }

            function sendChatMessage() {
                const input = document.getElementById('chat-input');
                const message = input.value.trim();

                if (message) {
                    addChatMessage(message, 'user');
                    input.value = '';

                    setTimeout(() => {
                        handleBotResponse(message);
                    }, 1000);
                }
            }

            function addChatMessage(message, sender) {
                const chatContainer = document.getElementById('chat-container');
                const messageDiv = document.createElement('div');
                messageDiv.className = `chat-message ${sender}-message`;

                messageDiv.innerHTML = `
                    <div class="message-content">
                        <p>${message}</p>
                        <small class="text-muted">${new Date().toLocaleTimeString()}</small>
                    </div>
                `;

                chatContainer.appendChild(messageDiv);
                chatContainer.scrollTop = chatContainer.scrollHeight;
            }

            function handleBotResponse(userMessage) {
                let response = "Thank you for sharing. I'm here to help you with your health journey.";

                const seriousKeywords = ['pain', 'chest', 'breathing', 'dizzy', 'emergency', 'help', 'urgent'];
                const medicationKeywords = ['medication', 'pills', 'medicine', 'dose', 'tablet'];

                if (seriousKeywords.some(keyword => userMessage.toLowerCase().includes(keyword))) {
                    response = "I'm concerned about what you've shared. I'm flagging this as a serious case and notifying the nearest clinic. Please stay calm, help is on the way.";
                    showNotification('Emergency services have been notified', 'warning');
                } else if (medicationKeywords.some(keyword => userMessage.toLowerCase().includes(keyword))) {
                    response = "I can help you with medication reminders. Have you taken your prescribed medication today?";
                } else if (userMessage.toLowerCase().includes('better')) {
                    response = "I'm so glad to hear you're feeling better! Your recovery brings joy to our Ubuntu community. Keep taking care of yourself.";
                }

                addChatMessage(response, 'bot');
            }

            function handleEmergencySubmit() {
                showNotification('Emergency alert sent! Help is on the way.', 'success');
                const modal = bootstrap.Modal.getInstance(document.getElementById('emergencyModal'));
                modal.hide();
                document.getElementById('emergency-form').reset();
            }

            function initiateVoiceCall() {
                showNotification('Connecting to Ubuntu Health Assistant...', 'info');
                setTimeout(() => {
                    showNotification('Voice call connected! Speak now.', 'success');
                }, 2000);
            }

            function startBarcodeScanner() {
                showNotification('Camera starting...', 'info');
                setTimeout(() => {
                    document.getElementById('scanner-result').style.display = 'block';
                    showNotification('Medication information loaded!', 'success');
                }, 3000);
            }

            function initializeHealthCharts() {
                // Blood pressure chart
                const bpCtx = document.getElementById('bpChart');
                if (bpCtx) {
                    new Chart(bpCtx, {
                        type: 'line',
                        data: {
                            labels: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri'],
                            datasets: [{
                                data: [120, 118, 122, 119, 120],
                                borderColor: '#DE3831',
                                backgroundColor: 'rgba(222, 56, 49, 0.1)',
                                tension: 0.4
                            }]
                        },
                        options: {
                            responsive: true,
                            plugins: { legend: { display: false } },
                            scales: { x: { display: false }, y: { display: false } }
                        }
                    });
                }

                // Glucose chart
                const glucoseCtx = document.getElementById('glucoseChart');
                if (glucoseCtx) {
                    new Chart(glucoseCtx, {
                        type: 'line',
                        data: {
                            labels: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri'],
                            datasets: [{
                                data: [5.8, 6.1, 5.9, 6.0, 5.8],
                                borderColor: '#002395',
                                backgroundColor: 'rgba(0, 35, 149, 0.1)',
                                tension: 0.4
                            }]
                        },
                        options: {
                            responsive: true,
                            plugins: { legend: { display: false } },
                            scales: { x: { display: false }, y: { display: false } }
                        }
                    });
                }
            }

            function showNotification(message, type = 'info') {
                const notificationSystem = document.getElementById('notification-system');
                const notification = document.createElement('div');
                notification.className = `notification ${type}`;
                notification.innerHTML = `
                    <div style="display: flex; align-items: center; gap: 0.5rem;">
                        <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'warning' ? 'exclamation-triangle' : 'info-circle'}"></i>
                        <span>${message}</span>
                    </div>
                `;

                notificationSystem.appendChild(notification);

                setTimeout(() => {
                    notification.style.animation = 'slideOutNotification 0.5s ease-in forwards';
                    setTimeout(() => notification.remove(), 500);
                }, 4000);
            }

            // Enhanced chat with typing indicator
            function showTypingIndicator() {
                const typingIndicator = document.getElementById('typing-indicator');
                typingIndicator.style.display = 'block';

                setTimeout(() => {
                    typingIndicator.style.display = 'none';
                }, 2000);
            }

            // Theme toggle functionality
            document.getElementById('theme-toggle').addEventListener('click', function() {
                document.body.classList.toggle('light-theme');
                showNotification('Theme switched! Ubuntu adapts to your preference 🎨', 'success');
            });

            // Enhanced card interactions
            document.querySelectorAll('.quick-action-card').forEach(card => {
                card.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-15px) scale(1.05)';
                });

                card.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0) scale(1)';
                });

                card.addEventListener('click', function() {
                    // Add ripple effect
                    const ripple = document.createElement('div');
                    ripple.style.cssText = `
                        position: absolute;
                        border-radius: 50%;
                        background: rgba(255, 255, 255, 0.3);
                        transform: scale(0);
                        animation: ripple 0.6s linear;
                        pointer-events: none;
                        width: 100px;
                        height: 100px;
                        left: 50%;
                        top: 50%;
                        margin-left: -50px;
                        margin-top: -50px;
                    `;

                    this.appendChild(ripple);

                    setTimeout(() => {
                        ripple.remove();
                    }, 600);
                });
            });

            // Auto-show typing indicator periodically
            setInterval(() => {
                if (Math.random() > 0.7) {
                    showTypingIndicator();
                }
            }, 15000);

            // Welcome animation sequence
            setTimeout(() => {
                showNotification('Welcome back, Thabo! Your health data has been updated 💚', 'success');
            }, 2000);

            setTimeout(() => {
                showNotification('Reminder: Take your Amlodipine in 30 minutes 💊', 'info');
            }, 8000);

            // Make showDailyTip global
            window.showDailyTip = function() {
                const tips = [
                    "Ubuntu teaches us that our well-being is connected to others. Share a healthy meal with family today.",
                    "Take a walk in nature - South Africa's beautiful landscapes can heal both body and soul.",
                    "Remember: 'I am because we are' - your health journey affects your whole community.",
                    "Practice gratitude today - it's good for your mental health and Ubuntu spirit.",
                    "Stay hydrated with clean water - a basic right for all South Africans."
                ];

                const randomTip = tips[Math.floor(Math.random() * tips.length)];
                showNotification(randomTip, 'info');
            };

            console.log('🇿🇦 Ubuntu Healthcare Dashboard Loaded - "I am because we are" 🇿🇦');
        });

        // Add notification animations
        const notificationStyle = document.createElement('style');
        notificationStyle.textContent = `
            @keyframes slideOutNotification {
                from { transform: translateX(0); opacity: 1; }
                to { transform: translateX(400px); opacity: 0; }
            }

            @keyframes ripple {
                to { transform: scale(4); opacity: 0; }
            }

            .light-theme {
                --dark-bg: #f5f7fa !important;
                --glass-bg: rgba(255, 255, 255, 0.8) !important;
                --card-bg: #ffffff !important;
                color: #2c3e50 !important;
            }

            .light-theme .navbar-brand,
            .light-theme .welcome-title,
            .light-theme .section-title,
            .light-theme h4, .light-theme h5, .light-theme h6 {
                color: #2c3e50 !important;
            }
        `;
        document.head.appendChild(notificationStyle);
    </script>
</body>
</html>
