// Ubuntu Healthcare SA - Main JavaScript

document.addEventListener('DOMContentLoaded', function() {
    // Smooth scrolling for anchor links
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });

    // Add animation classes when elements come into view
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver(function(entries) {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('fade-in');
            }
        });
    }, observerOptions);

    // Observe all feature cards and stat items
    document.querySelectorAll('.feature-card, .stat-item').forEach(el => {
        observer.observe(el);
    });

    // Add hover effects to buttons
    document.querySelectorAll('.btn-ubuntu-primary, .btn-ubuntu-secondary').forEach(btn => {
        btn.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-2px)';
        });
        
        btn.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0)';
        });
    });

    // Animate the pulse heart icon
    const heartIcon = document.querySelector('.pulse-animation');
    if (heartIcon) {
        setInterval(() => {
            heartIcon.style.color = heartIcon.style.color === 'rgb(222, 56, 49)' ? '#FF6B6B' : '#DE3831';
        }, 1000);
    }

    // Add loading animation to buttons when clicked
    document.querySelectorAll('.btn').forEach(btn => {
        btn.addEventListener('click', function() {
            if (!this.classList.contains('loading')) {
                this.classList.add('loading');
                const originalText = this.innerHTML;
                this.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Loading...';
                
                setTimeout(() => {
                    this.innerHTML = originalText;
                    this.classList.remove('loading');
                }, 1500);
            }
        });
    });

    // Add Ubuntu greeting based on time of day
    const now = new Date();
    const hour = now.getHours();
    let greeting = 'Sawubona'; // Default Zulu greeting
    
    if (hour < 12) {
        greeting = 'Sawubona'; // Good morning in Zulu
    } else if (hour < 17) {
        greeting = 'Sanibonani'; // Good afternoon in Zulu
    } else {
        greeting = 'Sawubona'; // Good evening in Zulu
    }

    // Update any greeting elements
    const greetingElements = document.querySelectorAll('.dynamic-greeting');
    greetingElements.forEach(el => {
        el.textContent = greeting;
    });

    // Add South African flag animation
    const flagStripes = document.querySelectorAll('.flag-stripe');
    flagStripes.forEach((stripe, index) => {
        stripe.style.animationDelay = `${index * 0.2}s`;
        stripe.classList.add('flag-animate');
    });

    // Console message with Ubuntu spirit
    console.log(`
    🇿🇦 Ubuntu Healthcare SA 🇿🇦
    
    "I am because we are" - Ubuntu Philosophy
    
    Built with love for Mzansi 💚💛💙
    
    Proudly South African Healthcare Solution
    `);
});

// Add CSS for flag animation
const style = document.createElement('style');
style.textContent = `
    .flag-animate {
        animation: flagWave 3s ease-in-out infinite;
    }
    
    @keyframes flagWave {
        0%, 100% { transform: scaleX(1); }
        50% { transform: scaleX(1.05); }
    }
    
    .loading {
        pointer-events: none;
        opacity: 0.7;
    }
    
    .fade-in {
        animation: fadeInUp 0.6s ease-out forwards;
    }
    
    @keyframes fadeInUp {
        from {
            opacity: 0;
            transform: translateY(30px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }
`;
document.head.appendChild(style);
