<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Ubuntu Healthcare SA - Sawubona Mzansi</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        /* Ubuntu Healthcare SA - Demo Styles */
        :root {
            --sa-green: #007749;
            --sa-gold: #FFB612;
            --sa-blue: #002395;
            --sa-red: #DE3831;
            --ubuntu-gradient: linear-gradient(135deg, var(--sa-green) 0%, var(--sa-blue) 100%);
            --gold-gradient: linear-gradient(135deg, var(--sa-gold) 0%, #FFA000 100%);
        }

        body {
            font-family: 'Se<PERSON>e UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            min-height: 100vh;
        }

        .ubuntu-header {
            background: var(--ubuntu-gradient);
            color: white;
            padding: 2rem 0;
            position: relative;
            overflow: hidden;
        }

        .ubuntu-header::before {
            content: '';
            position: absolute;
            top: 0; left: 0; right: 0; bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="50" cy="50" r="2" fill="rgba(255,255,255,0.1)"/></svg>') repeat;
            animation: float 20s infinite linear;
        }

        @keyframes float {
            0% { transform: translateY(0px); }
            100% { transform: translateY(-100px); }
        }

        .brand-badge {
            background: rgba(255, 255, 255, 0.2);
            padding: 0.5rem 1rem;
            border-radius: 25px;
            display: inline-block;
            margin-bottom: 1rem;
            backdrop-filter: blur(10px);
        }

        .ubuntu-title {
            font-size: 3.5rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .sawubona { color: var(--sa-gold); }
        .mzansi { color: white; }
        .ubuntu-text { color: var(--sa-gold); font-weight: 600; }

        .flag-colors {
            display: flex;
            flex-direction: column;
            width: 80px;
            height: 60px;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
        }

        .flag-stripe { flex: 1; }
        .flag-stripe.green { background: var(--sa-green); }
        .flag-stripe.gold { background: var(--sa-gold); }
        .flag-stripe.blue { background: var(--sa-blue); }

        .hero-section {
            padding: 4rem 0;
            background: white;
        }

        .hero-title {
            font-size: 2.5rem;
            font-weight: 700;
            color: #2c3e50;
            margin-bottom: 1.5rem;
        }

        .ubuntu-highlight {
            color: var(--sa-green);
            position: relative;
        }

        .ubuntu-highlight::after {
            content: '';
            position: absolute;
            bottom: -5px; left: 0;
            width: 100%;
            height: 3px;
            background: var(--gold-gradient);
            border-radius: 2px;
        }

        .hero-stats {
            display: flex;
            gap: 2rem;
            margin-bottom: 2rem;
            flex-wrap: wrap;
        }

        .stat-item {
            text-align: center;
            padding: 1rem;
            background: rgba(0, 119, 73, 0.1);
            border-radius: 12px;
            min-width: 120px;
        }

        .stat-item i {
            font-size: 1.5rem;
            color: var(--sa-green);
            margin-bottom: 0.5rem;
            display: block;
        }

        .stat-number {
            font-size: 1.8rem;
            font-weight: 700;
            color: var(--sa-green);
            display: block;
        }

        .btn-ubuntu-primary {
            background: var(--ubuntu-gradient);
            border: none;
            color: white;
            padding: 1rem 2rem;
            border-radius: 50px;
            font-weight: 600;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(0, 119, 73, 0.3);
        }

        .btn-ubuntu-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0, 119, 73, 0.4);
            color: white;
        }

        .btn-ubuntu-secondary {
            background: var(--gold-gradient);
            border: none;
            color: #2c3e50;
            padding: 1rem 2rem;
            border-radius: 50px;
            font-weight: 600;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            transition: all 0.3s ease;
        }

        .healthcare-illustration {
            position: relative;
            width: 300px;
            height: 300px;
            display: flex;
            justify-content: center;
            align-items: center;
            margin: 0 auto;
        }

        .pulse-animation {
            font-size: 4rem;
            color: var(--sa-red);
            animation: pulse 2s infinite;
            position: absolute;
        }

        @keyframes pulse {
            0% { transform: scale(1); opacity: 1; }
            50% { transform: scale(1.1); opacity: 0.7; }
            100% { transform: scale(1); opacity: 1; }
        }

        .ubuntu-circle {
            background: var(--ubuntu-gradient);
            width: 150px;
            height: 150px;
            border-radius: 50%;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            color: white;
            font-weight: 600;
            box-shadow: 0 10px 30px rgba(0, 119, 73, 0.3);
            position: relative;
            z-index: 2;
        }

        .features-section {
            padding: 4rem 0;
            background: #f8f9fa;
        }

        .feature-card {
            background: white;
            padding: 2rem;
            border-radius: 15px;
            text-align: center;
            height: 100%;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .feature-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 15px 30px rgba(0,0,0,0.15);
        }

        .ubuntu-card { border-top: 4px solid var(--sa-green); }
        .community-card { border-top: 4px solid var(--sa-gold); }
        .diversity-card { border-top: 4px solid var(--sa-blue); }

        .feature-icon {
            width: 80px;
            height: 80px;
            margin: 0 auto 1.5rem;
            background: var(--ubuntu-gradient);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 2rem;
        }

        .community-card .feature-icon {
            background: var(--gold-gradient);
            color: #2c3e50;
        }

        .diversity-card .feature-icon {
            background: linear-gradient(135deg, var(--sa-blue) 0%, #1565C0 100%);
        }

        .cta-section {
            padding: 4rem 0;
            background: var(--ubuntu-gradient);
            color: white;
            text-align: center;
        }

        .ubuntu-footer {
            background: #2c3e50;
            color: white;
            padding: 2rem 0;
        }

        .fade-in {
            animation: fadeInUp 0.6s ease-out forwards;
        }

        @keyframes fadeInUp {
            from { opacity: 0; transform: translateY(30px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @media (max-width: 768px) {
            .ubuntu-title { font-size: 2.5rem; }
            .hero-title { font-size: 2rem; }
            .hero-stats { justify-content: center; }
        }
    </style>
</head>
<body>
    <!-- Header with Ubuntu Healthcare Branding -->
    <header class="ubuntu-header">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <div class="brand-badge">
                        <i class="fas fa-heart-pulse"></i>
                        ZA Proudly Mzansi - Ubuntu Healthcare
                    </div>
                    <h1 class="ubuntu-title">
                        <span class="sawubona">Sawubona</span> 
                        <span class="mzansi">Mzansi</span>
                    </h1>
                    <p class="ubuntu-subtitle">Your Health, Our <span class="ubuntu-text">Ubuntu</span></p>
                </div>
                <div class="col-md-4 text-end">
                    <div class="sa-flag-element">
                        <div class="flag-colors">
                            <div class="flag-stripe green"></div>
                            <div class="flag-stripe gold"></div>
                            <div class="flag-stripe blue"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </header>

    <!-- Hero Section -->
    <section class="hero-section">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-lg-6">
                    <div class="hero-content">
                        <h2 class="hero-title">Healthcare with <span class="ubuntu-highlight">Ubuntu Spirit</span></h2>
                        <p class="hero-description">
                            "I am because we are" - Experience healthcare that embodies the true spirit of Ubuntu. 
                            Together, we build a healthier South Africa, one patient at a time.
                        </p>
                        <div class="hero-stats">
                            <div class="stat-item">
                                <i class="fas fa-users"></i>
                                <span class="stat-number">50,000+</span>
                                <span class="stat-label">Patients Served</span>
                            </div>
                            <div class="stat-item">
                                <i class="fas fa-hospital"></i>
                                <span class="stat-number">200+</span>
                                <span class="stat-label">Clinics Connected</span>
                            </div>
                            <div class="stat-item">
                                <i class="fas fa-language"></i>
                                <span class="stat-number">11</span>
                                <span class="stat-label">SA Languages</span>
                            </div>
                        </div>
                        <a href="dashboard-demo.html" class="btn btn-ubuntu-primary btn-lg">
                            <i class="fas fa-tachometer-alt"></i>
                            Access Your Dashboard
                        </a>
                    </div>
                </div>
                <div class="col-lg-6">
                    <div class="hero-image">
                        <div class="healthcare-illustration">
                            <i class="fas fa-heartbeat pulse-animation"></i>
                            <div class="ubuntu-circle">
                                <span>Ubuntu</span>
                                <small>Healthcare</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Features Section -->
    <section class="features-section">
        <div class="container">
            <div class="row">
                <div class="col-12 text-center mb-5">
                    <h3 class="section-title">Healthcare Features Rooted in <span class="ubuntu-text">Ubuntu Values</span></h3>
                    <p class="section-subtitle">Reflecting our commitment to unity, respect, and community care</p>
                </div>
            </div>
            <div class="row">
                <div class="col-md-4 mb-4">
                    <div class="feature-card ubuntu-card">
                        <div class="feature-icon">
                            <i class="fas fa-comments"></i>
                        </div>
                        <h4>Ubuntu Health Assistant</h4>
                        <p>AI-powered support that understands and respects our cultural values</p>
                    </div>
                </div>
                <div class="col-md-4 mb-4">
                    <div class="feature-card community-card">
                        <div class="feature-icon">
                            <i class="fas fa-users-medical"></i>
                        </div>
                        <h4>Community Care</h4>
                        <p>Connect with local clinics and healthcare providers in your area</p>
                    </div>
                </div>
                <div class="col-md-4 mb-4">
                    <div class="feature-card diversity-card">
                        <div class="feature-icon">
                            <i class="fas fa-globe-africa"></i>
                        </div>
                        <h4>Unity in Diversity</h4>
                        <p>Healthcare in all 11 official South African languages</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Call to Action -->
    <section class="cta-section">
        <div class="container">
            <div class="row">
                <div class="col-12 text-center">
                    <h3 class="cta-title">Ready to Experience Ubuntu Healthcare?</h3>
                    <p class="cta-description">Join thousands of South Africans who trust us with their health</p>
                    <a href="dashboard-demo.html" class="btn btn-ubuntu-secondary btn-lg me-3">
                        <i class="fas fa-sign-in-alt"></i>
                        Patient Dashboard
                    </a>
                    <button class="btn btn-outline-light btn-lg">
                        <i class="fas fa-user-plus"></i>
                        Register Now
                    </button>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="ubuntu-footer">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <p>&copy; 2024 Ubuntu Healthcare SA. Made with <i class="fas fa-heart text-danger"></i> for Mzansi</p>
                </div>
                <div class="col-md-6 text-end">
                    <p>Proudly South African | <span class="ubuntu-text">Ubuntu</span> Spirit</p>
                </div>
            </div>
        </div>
    </footer>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Add fade-in animation to elements
            setTimeout(() => {
                document.querySelectorAll('.feature-card, .stat-item').forEach((el, index) => {
                    setTimeout(() => el.classList.add('fade-in'), index * 100);
                });
            }, 500);

            console.log('🇿🇦 Ubuntu Healthcare SA - "I am because we are" 🇿🇦');
        });
    </script>
</body>
</html>
