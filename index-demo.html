<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Ubuntu Healthcare SA - Sawubona Mzansi</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        /* Ubuntu Healthcare SA - Modern Dark Theme */
        :root {
            --sa-green: #00B04F;
            --sa-gold: #FFB612;
            --sa-orange: #FF8C42;
            --dark-bg: #1a1a1a;
            --card-bg: #2a2a2a;
            --glass-bg: rgba(255, 255, 255, 0.1);
            --ubuntu-gradient: linear-gradient(135deg, var(--sa-green) 0%, #00D4AA 100%);
            --orange-gradient: linear-gradient(135deg, var(--sa-orange) 0%, #FF6B35 100%);
            --dark-gradient: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', 'Segoe UI', sans-serif;
            background: var(--dark-bg);
            color: white;
            min-height: 100vh;
            overflow-x: hidden;
        }

        /* Animated Background */
        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background:
                radial-gradient(circle at 20% 80%, rgba(0, 176, 79, 0.3) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(255, 140, 66, 0.3) 0%, transparent 50%),
                radial-gradient(circle at 40% 40%, rgba(255, 182, 18, 0.2) 0%, transparent 50%);
            animation: backgroundShift 20s ease-in-out infinite;
            z-index: -1;
        }

        @keyframes backgroundShift {
            0%, 100% { transform: scale(1) rotate(0deg); }
            50% { transform: scale(1.1) rotate(5deg); }
        }

        .ubuntu-header {
            background: var(--ubuntu-gradient);
            color: white;
            padding: 3rem 0;
            position: relative;
            overflow: hidden;
            border-radius: 0 0 30px 30px;
        }

        .ubuntu-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="20" cy="20" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="80" cy="40" r="1.5" fill="rgba(255,255,255,0.15)"/><circle cx="40" cy="80" r="1" fill="rgba(255,255,255,0.1)"/></svg>') repeat;
            animation: floatParticles 25s infinite linear;
        }

        @keyframes floatParticles {
            0% { transform: translateY(0px) translateX(0px); }
            100% { transform: translateY(-100px) translateX(20px); }
        }

        .brand-badge {
            background: var(--glass-bg);
            padding: 0.8rem 1.5rem;
            border-radius: 50px;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            margin-bottom: 1.5rem;
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            animation: glow 3s ease-in-out infinite alternate;
        }

        @keyframes glow {
            0% { box-shadow: 0 0 20px rgba(255, 255, 255, 0.2); }
            100% { box-shadow: 0 0 30px rgba(255, 255, 255, 0.4); }
        }

        .ubuntu-title {
            font-size: 4rem;
            font-weight: 800;
            margin-bottom: 1rem;
            background: linear-gradient(45deg, var(--sa-gold), #FFF, var(--sa-orange));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            animation: titleShine 4s ease-in-out infinite;
        }

        @keyframes titleShine {
            0%, 100% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
        }

        .sawubona {
            color: var(--sa-gold);
            text-shadow: 0 0 20px rgba(255, 182, 18, 0.5);
        }

        .mzansi {
            color: white;
            text-shadow: 0 0 20px rgba(255, 255, 255, 0.3);
        }

        .ubuntu-text {
            color: var(--sa-gold);
            font-weight: 700;
            text-shadow: 0 0 10px rgba(255, 182, 18, 0.3);
        }

        .hero-section {
            padding: 5rem 0;
            background: transparent;
            position: relative;
        }

        .hero-title {
            font-size: 3rem;
            font-weight: 700;
            color: white;
            margin-bottom: 2rem;
            text-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
        }

        .ubuntu-highlight {
            background: var(--orange-gradient);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            position: relative;
            animation: pulse 2s ease-in-out infinite;
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        .hero-description {
            font-size: 1.3rem;
            color: rgba(255, 255, 255, 0.9);
            margin-bottom: 3rem;
            line-height: 1.8;
            text-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
        }

        .hero-stats {
            display: flex;
            gap: 2rem;
            margin-bottom: 3rem;
            flex-wrap: wrap;
        }

        .stat-item {
            text-align: center;
            padding: 2rem 1.5rem;
            background: var(--glass-bg);
            border-radius: 20px;
            min-width: 140px;
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            transition: all 0.3s ease;
            animation: floatUp 0.6s ease-out forwards;
            opacity: 0;
            transform: translateY(30px);
        }

        .stat-item:nth-child(1) { animation-delay: 0.2s; }
        .stat-item:nth-child(2) { animation-delay: 0.4s; }
        .stat-item:nth-child(3) { animation-delay: 0.6s; }

        @keyframes floatUp {
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .stat-item:hover {
            transform: translateY(-10px);
            box-shadow: 0 20px 40px rgba(0, 176, 79, 0.3);
        }

        .stat-item i {
            font-size: 2rem;
            color: var(--sa-orange);
            margin-bottom: 1rem;
            display: block;
            animation: iconBounce 2s ease-in-out infinite;
        }

        @keyframes iconBounce {
            0%, 100% { transform: translateY(0); }
            50% { transform: translateY(-5px); }
        }

        .stat-number {
            font-size: 2.2rem;
            font-weight: 800;
            color: white;
            display: block;
            margin-bottom: 0.5rem;
        }

        .stat-label {
            color: rgba(255, 255, 255, 0.8);
            font-weight: 500;
        }

        .btn-ubuntu-primary {
            background: var(--orange-gradient);
            border: none;
            color: white;
            padding: 1.2rem 2.5rem;
            border-radius: 25px;
            font-weight: 600;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 0.8rem;
            transition: all 0.4s ease;
            box-shadow: 0 8px 25px rgba(255, 140, 66, 0.4);
            position: relative;
            overflow: hidden;
        }

        .btn-ubuntu-primary::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s;
        }

        .btn-ubuntu-primary:hover::before {
            left: 100%;
        }

        .btn-ubuntu-primary:hover {
            transform: translateY(-5px) scale(1.05);
            box-shadow: 0 15px 35px rgba(255, 140, 66, 0.6);
            color: white;
        }

        .btn-ubuntu-secondary {
            background: var(--glass-bg);
            border: 2px solid var(--sa-gold);
            color: white;
            padding: 1.2rem 2.5rem;
            border-radius: 25px;
            font-weight: 600;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 0.8rem;
            transition: all 0.4s ease;
            backdrop-filter: blur(20px);
        }

        .btn-ubuntu-secondary:hover {
            background: var(--sa-gold);
            color: var(--dark-bg);
            transform: translateY(-5px) scale(1.05);
            box-shadow: 0 15px 35px rgba(255, 182, 18, 0.4);
        }

        .healthcare-illustration {
            position: relative;
            width: 400px;
            height: 400px;
            display: flex;
            justify-content: center;
            align-items: center;
            margin: 0 auto;
        }

        .pulse-animation {
            font-size: 5rem;
            color: var(--sa-orange);
            animation: heartPulse 2s infinite;
            position: absolute;
            filter: drop-shadow(0 0 20px rgba(255, 140, 66, 0.6));
        }

        @keyframes heartPulse {
            0% { transform: scale(1); opacity: 1; }
            50% { transform: scale(1.2); opacity: 0.8; }
            100% { transform: scale(1); opacity: 1; }
        }

        .ubuntu-circle {
            background: var(--glass-bg);
            backdrop-filter: blur(20px);
            border: 2px solid rgba(255, 255, 255, 0.2);
            width: 180px;
            height: 180px;
            border-radius: 50%;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            color: white;
            font-weight: 700;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
            position: relative;
            z-index: 2;
            animation: float 6s ease-in-out infinite;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-20px); }
        }

        .ubuntu-circle span {
            font-size: 1.4rem;
            text-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
        }

        .ubuntu-circle small {
            font-size: 0.9rem;
            opacity: 0.9;
            color: var(--sa-gold);
        }

        .features-section {
            padding: 5rem 0;
            background: transparent;
            position: relative;
        }

        .section-title {
            font-size: 2.5rem;
            font-weight: 700;
            color: white;
            margin-bottom: 1rem;
            text-align: center;
        }

        .section-subtitle {
            font-size: 1.2rem;
            color: rgba(255, 255, 255, 0.8);
            margin-bottom: 4rem;
            text-align: center;
        }

        .feature-card {
            background: var(--glass-bg);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            padding: 2.5rem 2rem;
            border-radius: 25px;
            text-align: center;
            height: 100%;
            transition: all 0.4s ease;
            position: relative;
            overflow: hidden;
        }

        .feature-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.1), transparent);
            transform: translateX(-100%);
            transition: transform 0.6s;
        }

        .feature-card:hover::before {
            transform: translateX(100%);
        }

        .feature-card:hover {
            transform: translateY(-15px) scale(1.02);
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.3);
            border-color: var(--sa-orange);
        }

        .ubuntu-card:hover { box-shadow: 0 25px 50px rgba(0, 176, 79, 0.4); }
        .community-card:hover { box-shadow: 0 25px 50px rgba(255, 182, 18, 0.4); }
        .diversity-card:hover { box-shadow: 0 25px 50px rgba(255, 140, 66, 0.4); }

        .feature-icon {
            width: 90px;
            height: 90px;
            margin: 0 auto 2rem;
            background: var(--orange-gradient);
            border-radius: 25px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 2.2rem;
            box-shadow: 0 10px 25px rgba(255, 140, 66, 0.3);
            animation: iconFloat 3s ease-in-out infinite;
        }

        @keyframes iconFloat {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-10px) rotate(5deg); }
        }

        .community-card .feature-icon {
            background: var(--ubuntu-gradient);
            box-shadow: 0 10px 25px rgba(0, 176, 79, 0.3);
        }

        .diversity-card .feature-icon {
            background: linear-gradient(135deg, var(--sa-gold) 0%, #FFD700 100%);
            box-shadow: 0 10px 25px rgba(255, 182, 18, 0.3);
        }

        .feature-card h4 {
            font-size: 1.4rem;
            font-weight: 700;
            margin-bottom: 1rem;
            color: white;
        }

        .feature-card p {
            color: rgba(255, 255, 255, 0.8);
            line-height: 1.6;
            font-size: 1rem;
        }

        .cta-section {
            padding: 5rem 0;
            background: var(--dark-gradient);
            color: white;
            text-align: center;
            position: relative;
            border-radius: 30px 30px 0 0;
            margin-top: 3rem;
        }

        .cta-title {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 1.5rem;
            background: linear-gradient(45deg, var(--sa-gold), var(--sa-orange));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .cta-description {
            font-size: 1.3rem;
            margin-bottom: 3rem;
            opacity: 0.9;
            color: rgba(255, 255, 255, 0.8);
        }

        .ubuntu-footer {
            background: var(--dark-bg);
            color: rgba(255, 255, 255, 0.8);
            padding: 3rem 0;
            border-top: 1px solid rgba(255, 255, 255, 0.1);
        }

        .ubuntu-footer p {
            margin: 0;
            font-size: 1rem;
        }

        .fade-in {
            animation: fadeInUp 0.6s ease-out forwards;
        }

        @keyframes fadeInUp {
            from { opacity: 0; transform: translateY(30px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @media (max-width: 768px) {
            .ubuntu-title { font-size: 2.5rem; }
            .hero-title { font-size: 2rem; }
            .hero-stats { justify-content: center; }
        }
    </style>
</head>
<body>
    <!-- Header with Ubuntu Healthcare Branding -->
    <header class="ubuntu-header">
        <div class="container">
            <div class="row align-items-center justify-content-center">
                <div class="col-12 text-center">
                    <div class="brand-badge">
                        <i class="fas fa-heart-pulse"></i>
                        🇿🇦 Proudly Mzansi - Ubuntu Healthcare
                    </div>
                    <h1 class="ubuntu-title">
                        <span class="sawubona">Sawubona</span>
                        <span class="mzansi">Mzansi</span>
                    </h1>
                    <p class="ubuntu-subtitle">Your Health, Our <span class="ubuntu-text">Ubuntu</span></p>
                </div>
            </div>
        </div>
    </header>

    <!-- Hero Section -->
    <section class="hero-section">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-lg-6">
                    <div class="hero-content">
                        <h2 class="hero-title">Healthcare with <span class="ubuntu-highlight">Ubuntu Spirit</span></h2>
                        <p class="hero-description">
                            "I am because we are" - Experience healthcare that embodies the true spirit of Ubuntu. 
                            Together, we build a healthier South Africa, one patient at a time.
                        </p>
                        <div class="hero-stats">
                            <div class="stat-item">
                                <i class="fas fa-users"></i>
                                <span class="stat-number">50,000+</span>
                                <span class="stat-label">Patients Served</span>
                            </div>
                            <div class="stat-item">
                                <i class="fas fa-hospital"></i>
                                <span class="stat-number">200+</span>
                                <span class="stat-label">Clinics Connected</span>
                            </div>
                            <div class="stat-item">
                                <i class="fas fa-language"></i>
                                <span class="stat-number">11</span>
                                <span class="stat-label">SA Languages</span>
                            </div>
                        </div>
                        <a href="dashboard-demo.html" class="btn btn-ubuntu-primary btn-lg">
                            <i class="fas fa-tachometer-alt"></i>
                            Access Your Dashboard
                        </a>
                    </div>
                </div>
                <div class="col-lg-6">
                    <div class="hero-image">
                        <div class="healthcare-illustration">
                            <i class="fas fa-heartbeat pulse-animation"></i>
                            <div class="ubuntu-circle">
                                <span>Ubuntu</span>
                                <small>Healthcare</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Features Section -->
    <section class="features-section">
        <div class="container">
            <div class="row">
                <div class="col-12 text-center mb-5">
                    <h3 class="section-title">Healthcare Features Rooted in <span class="ubuntu-text">Ubuntu Values</span></h3>
                    <p class="section-subtitle">Reflecting our commitment to unity, respect, and community care</p>
                </div>
            </div>
            <div class="row">
                <div class="col-md-4 mb-4">
                    <div class="feature-card ubuntu-card">
                        <div class="feature-icon">
                            <i class="fas fa-comments"></i>
                        </div>
                        <h4>Ubuntu Health Assistant</h4>
                        <p>AI-powered support that understands and respects our cultural values</p>
                    </div>
                </div>
                <div class="col-md-4 mb-4">
                    <div class="feature-card community-card">
                        <div class="feature-icon">
                            <i class="fas fa-users-medical"></i>
                        </div>
                        <h4>Community Care</h4>
                        <p>Connect with local clinics and healthcare providers in your area</p>
                    </div>
                </div>
                <div class="col-md-4 mb-4">
                    <div class="feature-card diversity-card">
                        <div class="feature-icon">
                            <i class="fas fa-globe-africa"></i>
                        </div>
                        <h4>Unity in Diversity</h4>
                        <p>Healthcare in all 11 official South African languages</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Call to Action -->
    <section class="cta-section">
        <div class="container">
            <div class="row">
                <div class="col-12 text-center">
                    <h3 class="cta-title">Ready to Experience Ubuntu Healthcare?</h3>
                    <p class="cta-description">Join thousands of South Africans who trust us with their health</p>
                    <a href="dashboard-demo.html" class="btn btn-ubuntu-secondary btn-lg me-3">
                        <i class="fas fa-sign-in-alt"></i>
                        Patient Dashboard
                    </a>
                    <button class="btn btn-outline-light btn-lg">
                        <i class="fas fa-user-plus"></i>
                        Register Now
                    </button>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="ubuntu-footer">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <p>&copy; 2024 Ubuntu Healthcare SA. Made with <i class="fas fa-heart text-danger"></i> for Mzansi</p>
                </div>
                <div class="col-md-6 text-end">
                    <p>Proudly South African | <span class="ubuntu-text">Ubuntu</span> Spirit</p>
                </div>
            </div>
        </div>
    </footer>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Add fade-in animation to elements
            setTimeout(() => {
                document.querySelectorAll('.feature-card, .stat-item').forEach((el, index) => {
                    setTimeout(() => el.classList.add('fade-in'), index * 100);
                });
            }, 500);

            console.log('🇿🇦 Ubuntu Healthcare SA - "I am because we are" 🇿🇦');
        });
    </script>
</body>
</html>
