/* Ubuntu Healthcare SA - Dashboard Styles */

:root {
    /* South African Flag Colors */
    --sa-green: #007749;
    --sa-gold: #FFB612;
    --sa-blue: #002395;
    --sa-red: #DE3831;
    --sa-black: #000000;
    --sa-white: #FFFFFF;
    
    /* Ubuntu Theme Colors */
    --ubuntu-primary: #007749;
    --ubuntu-secondary: #FFB612;
    --ubuntu-accent: #002395;
    --ubuntu-light: #f8f9fa;
    --ubuntu-dark: #2c3e50;
    
    /* Gradients */
    --ubuntu-gradient: linear-gradient(135deg, var(--sa-green) 0%, var(--sa-blue) 100%);
    --gold-gradient: linear-gradient(135deg, var(--sa-gold) 0%, #FFA000 100%);
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: #f5f7fa;
    color: var(--ubuntu-dark);
}

/* Navigation Bar */
.ubuntu-navbar {
    background: white;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    padding: 1rem 0;
}

.navbar-brand {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--ubuntu-primary) !important;
}

.brand-text {
    margin-left: 0.5rem;
}

.language-selector {
    color: var(--ubuntu-primary) !important;
    font-weight: 500;
}

.language-selector:hover {
    color: var(--sa-gold) !important;
}

/* Welcome Card */
.welcome-card {
    background: var(--ubuntu-gradient);
    color: white;
    padding: 2rem;
    border-radius: 15px;
    margin-bottom: 2rem;
    box-shadow: 0 5px 20px rgba(0, 119, 73, 0.3);
}

.welcome-title {
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
}

.welcome-subtitle {
    font-size: 1.1rem;
    opacity: 0.9;
    margin: 0;
}

.ubuntu-symbol {
    text-align: center;
    background: rgba(255, 255, 255, 0.2);
    padding: 1rem;
    border-radius: 50%;
    width: 80px;
    height: 80px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    backdrop-filter: blur(10px);
}

.ubuntu-symbol i {
    font-size: 1.5rem;
    margin-bottom: 0.2rem;
}

.ubuntu-symbol span {
    font-size: 0.8rem;
    font-weight: 600;
}

/* Chat Monitor */
.chat-monitor-card {
    height: 500px;
    display: flex;
    flex-direction: column;
}

.bg-ubuntu {
    background: var(--ubuntu-gradient) !important;
}

.chat-container {
    flex: 1;
    overflow-y: auto;
    padding: 1rem;
    background: #f8f9fa;
}

.chat-message {
    margin-bottom: 1rem;
}

.bot-message .message-content {
    background: var(--ubuntu-primary);
    color: white;
    padding: 0.8rem 1rem;
    border-radius: 15px 15px 15px 5px;
    max-width: 80%;
}

.user-message .message-content {
    background: var(--sa-gold);
    color: var(--ubuntu-dark);
    padding: 0.8rem 1rem;
    border-radius: 15px 15px 5px 15px;
    max-width: 80%;
    margin-left: auto;
}

.chat-input-container {
    padding: 1rem;
    background: white;
    border-top: 1px solid #dee2e6;
}

.btn-ubuntu {
    background: var(--ubuntu-primary);
    border: none;
    color: white;
}

.btn-ubuntu:hover {
    background: #005a37;
    color: white;
}

/* Section Titles */
.section-title {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--ubuntu-dark);
    margin-bottom: 1rem;
    border-left: 4px solid var(--ubuntu-primary);
    padding-left: 1rem;
}

/* Quick Action Cards */
.quick-action-card {
    background: white;
    padding: 1.5rem;
    border-radius: 12px;
    text-align: center;
    height: 100%;
    transition: all 0.3s ease;
    cursor: pointer;
    border: 2px solid transparent;
    box-shadow: 0 3px 10px rgba(0,0,0,0.1);
}

.quick-action-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
    border-color: var(--ubuntu-primary);
}

.appointments-card {
    border-top: 4px solid var(--ubuntu-primary);
}

.medications-card {
    border-top: 4px solid var(--sa-gold);
}

.scanner-card {
    border-top: 4px solid var(--sa-blue);
}

.tips-card {
    border-top: 4px solid var(--sa-red);
}

.card-icon {
    width: 60px;
    height: 60px;
    margin: 0 auto 1rem;
    background: var(--ubuntu-gradient);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.5rem;
}

.medications-card .card-icon {
    background: var(--gold-gradient);
    color: var(--ubuntu-dark);
}

.scanner-card .card-icon {
    background: linear-gradient(135deg, var(--sa-blue) 0%, #1565C0 100%);
}

.tips-card .card-icon {
    background: linear-gradient(135deg, var(--sa-red) 0%, #C62828 100%);
}

.quick-action-card h6 {
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: var(--ubuntu-dark);
}

.quick-action-card p {
    font-weight: 500;
    margin-bottom: 0.2rem;
    color: var(--ubuntu-primary);
}

.quick-action-card small {
    color: #666;
}

/* Health Cards */
.health-card {
    border: none;
    border-radius: 12px;
    box-shadow: 0 3px 15px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
}

.health-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

.hypertension-card {
    border-left: 5px solid var(--sa-red);
}

.diabetes-card {
    border-left: 5px solid var(--sa-blue);
}

.health-reading {
    font-size: 1.5rem;
    font-weight: 700;
    margin: 0.5rem 0;
    color: var(--ubuntu-dark);
}

.health-chart {
    width: 100px;
    height: 60px;
}

/* Info Cards */
.info-card {
    border: none;
    border-radius: 12px;
    box-shadow: 0 3px 15px rgba(0,0,0,0.1);
    margin-bottom: 1.5rem;
}

.info-card .card-header {
    border-bottom: 1px solid #dee2e6;
    border-radius: 12px 12px 0 0 !important;
}

.medication-item {
    padding: 0.8rem 0;
    border-bottom: 1px solid #f1f3f4;
}

.medication-item:last-child {
    border-bottom: none;
}

.medication-name {
    font-weight: 600;
    color: var(--ubuntu-dark);
}

.appointment-item {
    display: flex;
    align-items: center;
    padding: 1rem 0;
    border-bottom: 1px solid #f1f3f4;
}

.appointment-item:last-child {
    border-bottom: none;
}

.appointment-date {
    background: var(--ubuntu-gradient);
    color: white;
    padding: 0.8rem;
    border-radius: 10px;
    text-align: center;
    margin-right: 1rem;
    min-width: 60px;
}

.appointment-date .date {
    font-size: 1.2rem;
    font-weight: 700;
    display: block;
}

.appointment-date .month {
    font-size: 0.8rem;
    opacity: 0.9;
}

.appointment-details h6 {
    margin-bottom: 0.3rem;
    color: var(--ubuntu-dark);
}

.appointment-details p {
    margin-bottom: 0.2rem;
    color: var(--ubuntu-primary);
    font-weight: 500;
}

/* Report Stats */
.report-stat {
    text-align: center;
    padding: 1rem;
    background: rgba(0, 119, 73, 0.05);
    border-radius: 10px;
    margin-bottom: 1rem;
}

.report-stat h4 {
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 0.3rem;
}

.report-stat small {
    color: #666;
    font-weight: 500;
}

/* Modal Styles */
.modal-content {
    border: none;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.3);
}

.modal-header {
    border-bottom: 1px solid #dee2e6;
    border-radius: 15px 15px 0 0;
}

.scanner-area {
    padding: 2rem;
    border: 2px dashed #dee2e6;
    border-radius: 10px;
    background: #f8f9fa;
}

/* Responsive Design */
@media (max-width: 768px) {
    .welcome-title {
        font-size: 1.5rem;
    }
    
    .welcome-subtitle {
        font-size: 1rem;
    }
    
    .ubuntu-symbol {
        width: 60px;
        height: 60px;
    }
    
    .chat-monitor-card {
        height: 400px;
        margin-bottom: 2rem;
    }
    
    .quick-action-card {
        margin-bottom: 1rem;
    }
    
    .card-icon {
        width: 50px;
        height: 50px;
        font-size: 1.2rem;
    }
    
    .health-reading {
        font-size: 1.2rem;
    }
    
    .appointment-date {
        min-width: 50px;
        padding: 0.6rem;
    }
    
    .section-title {
        font-size: 1.3rem;
    }
}

/* Animation Classes */
.fade-in {
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.pulse {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}
