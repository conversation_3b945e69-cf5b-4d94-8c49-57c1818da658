<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Patient Dashboard - Ubuntu Healthcare SA</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link rel="stylesheet" th:href="@{/css/dashboard.css}">
</head>
<body>
    <!-- Top Navigation Bar -->
    <nav class="navbar navbar-expand-lg ubuntu-navbar">
        <div class="container-fluid">
            <div class="navbar-brand">
                <i class="fas fa-heart-pulse text-danger"></i>
                <span class="brand-text">Ubuntu Healthcare</span>
            </div>
            
            <!-- Language Selection -->
            <div class="navbar-nav me-auto">
                <div class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle language-selector" href="#" role="button" data-bs-toggle="dropdown">
                        <i class="fas fa-globe"></i>
                        <span id="current-language">English</span>
                    </a>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item" href="#" data-lang="en">English</a></li>
                        <li><a class="dropdown-item" href="#" data-lang="zu">isiZulu</a></li>
                        <li><a class="dropdown-item" href="#" data-lang="xh">isiXhosa</a></li>
                        <li><a class="dropdown-item" href="#" data-lang="af">Afrikaans</a></li>
                        <li><a class="dropdown-item" href="#" data-lang="st">Sesotho</a></li>
                        <li><a class="dropdown-item" href="#" data-lang="tn">Setswana</a></li>
                        <li><a class="dropdown-item" href="#" data-lang="ve">Tshivenda</a></li>
                        <li><a class="dropdown-item" href="#" data-lang="ts">Xitsonga</a></li>
                        <li><a class="dropdown-item" href="#" data-lang="ss">siSwati</a></li>
                        <li><a class="dropdown-item" href="#" data-lang="nr">isiNdebele</a></li>
                        <li><a class="dropdown-item" href="#" data-lang="nso">Sepedi</a></li>
                    </ul>
                </div>
            </div>

            <!-- Right side navigation -->
            <div class="navbar-nav">
                <!-- Voice Call -->
                <button class="btn btn-outline-success me-2" id="voice-call-btn">
                    <i class="fas fa-phone"></i>
                    <span class="d-none d-md-inline">Voice Call</span>
                </button>
                
                <!-- Emergency Support -->
                <button class="btn btn-danger me-2" id="emergency-btn" data-bs-toggle="modal" data-bs-target="#emergencyModal">
                    <i class="fas fa-exclamation-triangle"></i>
                    <span class="d-none d-md-inline">Emergency</span>
                </button>
                
                <!-- Logout -->
                <button class="btn btn-outline-secondary">
                    <i class="fas fa-sign-out-alt"></i>
                    <span class="d-none d-md-inline">Logout</span>
                </button>
            </div>
        </div>
    </nav>

    <!-- Welcome Section -->
    <div class="container-fluid mt-3">
        <div class="row">
            <div class="col-12">
                <div class="welcome-card ubuntu-gradient">
                    <div class="row align-items-center">
                        <div class="col-md-8">
                            <h2 class="welcome-title">Sawubona, <span th:text="${patientName}">Thabo</span>!</h2>
                            <p class="welcome-subtitle">Your health journey continues with Ubuntu spirit - we're here for you</p>
                        </div>
                        <div class="col-md-4 text-end">
                            <div class="ubuntu-symbol">
                                <i class="fas fa-users"></i>
                                <span>Ubuntu</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Dashboard Content -->
    <div class="container-fluid">
        <div class="row">
            <!-- Left Sidebar - Chat Monitor -->
            <div class="col-lg-3">
                <div class="card chat-monitor-card">
                    <div class="card-header bg-ubuntu">
                        <h5 class="card-title text-white mb-0">
                            <i class="fas fa-robot"></i>
                            Chat Monitor
                        </h5>
                    </div>
                    <div class="card-body p-0">
                        <div class="chat-container" id="chat-container">
                            <div class="chat-message bot-message">
                                <div class="message-content">
                                    <p>Sawubona! I'm your Ubuntu Health Assistant. How are you feeling today?</p>
                                    <small class="text-muted">Just now</small>
                                </div>
                            </div>
                        </div>
                        <div class="chat-input-container">
                            <div class="input-group">
                                <input type="text" class="form-control" id="chat-input" placeholder="Type your message...">
                                <button class="btn btn-ubuntu" type="button" id="send-chat">
                                    <i class="fas fa-paper-plane"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Main Content Area -->
            <div class="col-lg-9">
                <!-- Quick Actions Row -->
                <div class="row mb-4">
                    <div class="col-12">
                        <h4 class="section-title">Quick Actions</h4>
                    </div>
                    <div class="col-md-3 mb-3">
                        <div class="quick-action-card appointments-card">
                            <div class="card-icon">
                                <i class="fas fa-calendar-check"></i>
                            </div>
                            <h6>Next Appointment</h6>
                            <p class="appointment-time" th:text="${nextAppointment}">Jan 15, 10:00 AM</p>
                            <small class="text-muted">Dr. Mandela Clinic</small>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <div class="quick-action-card medications-card">
                            <div class="card-icon">
                                <i class="fas fa-pills"></i>
                            </div>
                            <h6>Medications</h6>
                            <p class="medication-count" th:text="${medicationCount} + ' Active'">3 Active</p>
                            <small class="text-muted">View all medications</small>
                        </div>
                    </div>

                <!-- Health Monitoring Section -->
                <div class="row mb-4">
                    <div class="col-12">
                        <h4 class="section-title">Health Monitoring</h4>
                    </div>
                    <div class="col-md-6 mb-3">
                        <div class="card health-card hypertension-card">
                            <div class="card-body">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        <h6 class="card-title">
                                            <i class="fas fa-heartbeat text-danger"></i>
                                            Hypertension
                                        </h6>
                                        <p class="health-reading">120/80 mmHg</p>
                                        <small class="text-success">Normal Range</small>
                                    </div>
                                    <div class="health-chart">
                                        <canvas id="bpChart" width="100" height="60"></canvas>
                                    </div>
                                </div>
                                <button class="btn btn-sm btn-outline-primary mt-2">
                                    <i class="fas fa-upload"></i>
                                    Upload Reading
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6 mb-3">
                        <div class="card health-card diabetes-card">
                            <div class="card-body">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        <h6 class="card-title">
                                            <i class="fas fa-tint text-primary"></i>
                                            Diabetes
                                        </h6>
                                        <p class="health-reading">5.8 mmol/L</p>
                                        <small class="text-warning">Monitor Closely</small>
                                    </div>
                                    <div class="health-chart">
                                        <canvas id="glucoseChart" width="100" height="60"></canvas>
                                    </div>
                                </div>
                                <button class="btn btn-sm btn-outline-primary mt-2">
                                    <i class="fas fa-upload"></i>
                                    Upload Reading
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Detailed Information Cards -->
                <div class="row">
                    <div class="col-md-6 mb-4">
                        <div class="card info-card">
                            <div class="card-header bg-light">
                                <h6 class="mb-0">
                                    <i class="fas fa-pills text-success"></i>
                                    Current Medications
                                </h6>
                            </div>
                            <div class="card-body">
                                <div class="medication-item">
                                    <div class="d-flex justify-content-between">
                                        <span class="medication-name">Amlodipine 5mg</span>
                                        <span class="badge bg-success">Active</span>
                                    </div>
                                    <small class="text-muted">Once daily - 6 months</small>
                                </div>
                                <div class="medication-item">
                                    <div class="d-flex justify-content-between">
                                        <span class="medication-name">Metformin 500mg</span>
                                        <span class="badge bg-success">Active</span>
                                    </div>
                                    <small class="text-muted">Twice daily - 1 year</small>
                                </div>
                                <div class="medication-item">
                                    <div class="d-flex justify-content-between">
                                        <span class="medication-name">Vitamin D3</span>
                                        <span class="badge bg-success">Active</span>
                                    </div>
                                    <small class="text-muted">Once daily - 3 months</small>
                                </div>
                                <button class="btn btn-sm btn-ubuntu mt-2 w-100">View All Medications</button>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-6 mb-4">
                        <div class="card info-card">
                            <div class="card-header bg-light">
                                <h6 class="mb-0">
                                    <i class="fas fa-calendar-alt text-primary"></i>
                                    Upcoming Appointments
                                </h6>
                            </div>
                            <div class="card-body">
                                <div class="appointment-item">
                                    <div class="appointment-date">
                                        <span class="date">15</span>
                                        <span class="month">Jan</span>
                                    </div>
                                    <div class="appointment-details">
                                        <h6>Dr. Sarah Mthembu</h6>
                                        <p class="mb-1">General Checkup</p>
                                        <small class="text-muted">10:00 AM - Mandela Clinic</small>
                                    </div>
                                </div>
                                <div class="appointment-item">
                                    <div class="appointment-date">
                                        <span class="date">22</span>
                                        <span class="month">Jan</span>
                                    </div>
                                    <div class="appointment-details">
                                        <h6>Dr. John Radebe</h6>
                                        <p class="mb-1">Diabetes Follow-up</p>
                                        <small class="text-muted">2:30 PM - Ubuntu Health Center</small>
                                    </div>
                                </div>
                                <button class="btn btn-sm btn-ubuntu mt-2 w-100">View All Appointments</button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Medical Report Summary -->
                <div class="row">
                    <div class="col-12">
                        <div class="card info-card">
                            <div class="card-header bg-light">
                                <h6 class="mb-0">
                                    <i class="fas fa-file-medical text-info"></i>
                                    Medical Report Summary
                                </h6>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-3">
                                        <div class="report-stat">
                                            <h4 class="text-success">Good</h4>
                                            <small>Overall Health</small>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="report-stat">
                                            <h4 class="text-primary">2</h4>
                                            <small>Conditions Monitored</small>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="report-stat">
                                            <h4 class="text-warning">1</h4>
                                            <small>Pending Tests</small>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="report-stat">
                                            <h4 class="text-info">95%</h4>
                                            <small>Medication Adherence</small>
                                        </div>
                                    </div>
                                </div>
                                <button class="btn btn-ubuntu mt-3">
                                    <i class="fas fa-download"></i>
                                    Download Full Report
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Emergency Modal -->
    <div class="modal fade" id="emergencyModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header bg-danger text-white">
                    <h5 class="modal-title">
                        <i class="fas fa-exclamation-triangle"></i>
                        Emergency Support
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="emergency-form">
                        <div class="mb-3">
                            <label for="emergency-name" class="form-label">Full Name</label>
                            <input type="text" class="form-control" id="emergency-name" required>
                        </div>
                        <div class="mb-3">
                            <label for="emergency-surname" class="form-label">Surname</label>
                            <input type="text" class="form-control" id="emergency-surname" required>
                        </div>
                        <div class="mb-3">
                            <label for="emergency-id" class="form-label">ID Number</label>
                            <input type="text" class="form-control" id="emergency-id" required>
                        </div>
                        <div class="mb-3">
                            <label for="emergency-type" class="form-label">Emergency Type</label>
                            <select class="form-select" id="emergency-type" required>
                                <option value="">Select emergency type</option>
                                <option value="chest-pain">Chest Pain</option>
                                <option value="breathing">Difficulty Breathing</option>
                                <option value="injury">Serious Injury</option>
                                <option value="allergic">Allergic Reaction</option>
                                <option value="other">Other</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label for="emergency-description" class="form-label">Description</label>
                            <textarea class="form-control" id="emergency-description" rows="3" required></textarea>
                        </div>
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle"></i>
                            This will notify the nearest clinic, your doctor, and any linked relatives immediately.
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-danger" id="submit-emergency">
                        <i class="fas fa-ambulance"></i>
                        Send Emergency Alert
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Barcode Scanner Modal -->
    <div class="modal fade" id="scannerModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="fas fa-qrcode"></i>
                        Medication Barcode Scanner
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="text-center">
                        <div class="scanner-area">
                            <i class="fas fa-camera fa-3x text-muted mb-3"></i>
                            <p>Position the medication barcode within the frame</p>
                            <button class="btn btn-ubuntu" id="start-scanner">
                                <i class="fas fa-camera"></i>
                                Start Camera
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script th:src="@{/js/dashboard.js}"></script>
</body>
</html>
