// Ubuntu Healthcare SA - Dashboard JavaScript

document.addEventListener('DOMContentLoaded', function() {
    
    // Language translations for South African languages
    const translations = {
        'en': {
            'welcome': 'Welcome',
            'dashboard': 'Dashboard',
            'chat_monitor': 'Chat Monitor',
            'emergency': 'Emergency',
            'voice_call': 'Voice Call',
            'logout': 'Logout',
            'next_appointment': 'Next Appointment',
            'medications': 'Medications',
            'daily_tips': 'Daily Health Tips',
            'barcode_scanner': 'Barcode Scanner',
            'health_assistant': 'Ubuntu Health Assistant',
            'how_are_you': '<PERSON><PERSON><PERSON>! How are you feeling today?'
        },
        'zu': {
            'welcome': '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>a',
            'dashboard': '<PERSON><PERSON><PERSON><PERSON>',
            'chat_monitor': 'U<PERSON>qapha Ingxoxo',
            'emergency': '<PERSON><PERSON>',
            'voice_call': 'Ucing<PERSON>z<PERSON>',
            'logout': '<PERSON><PERSON>',
            'next_appointment': '<PERSON><PERSON><PERSON><PERSON>',
            'medications': 'Imithi',
            'daily_tips': '<PERSON><PERSON><PERSON><PERSON>',
            'barcode_scanner': '<PERSON><PERSON><PERSON>',
            'health_assistant': '<PERSON><PERSON><PERSON>zempilo We-Ubu<PERSON>u',
            'how_are_you': 'Sawubona! Uzizwa kanjani namuhla?'
        },
        'xh': {
            'welcome': 'Wamkelekile',
            'dashboard': 'Ibhodi Yolawulo',
            'chat_monitor': 'Ukubeka Iliso Kuncoko',
            'emergency': 'Imeko Yongxamiseko',
            'voice_call': 'Umnxeba Wezwi',
            'logout': 'Phuma',
            'next_appointment': 'Idinga Elilandelayo',
            'medications': 'Amayeza',
            'daily_tips': 'Iingcebiso Zempilo',
            'barcode_scanner': 'Isikena Sekhowudi',
            'health_assistant': 'Umncedisi Wezempilo We-Ubuntu',
            'how_are_you': 'Molo! Uziva njani namhlanje?'
        },
        'af': {
            'welcome': 'Welkom',
            'dashboard': 'Kontrolepaneel',
            'chat_monitor': 'Klets Monitor',
            'emergency': 'Noodgeval',
            'voice_call': 'Stemoproep',
            'logout': 'Teken Uit',
            'next_appointment': 'Volgende Afspraak',
            'medications': 'Medikasie',
            'daily_tips': 'Daaglikse Gesondheids Wenke',
            'barcode_scanner': 'Strepieskode Skandeerder',
            'health_assistant': 'Ubuntu Gesondheids Assistent',
            'how_are_you': 'Hallo! Hoe voel jy vandag?'
        }
    };

    let currentLanguage = 'en';
    let chatMessages = [];
    let medicationReminders = [];

    // Initialize dashboard
    initializeDashboard();
    setupEventListeners();
    startChatMonitoring();
    initializeHealthCharts();

    function initializeDashboard() {
        // Add welcome animation
        setTimeout(() => {
            document.querySelector('.welcome-card').classList.add('fade-in');
        }, 300);

        // Animate quick action cards
        document.querySelectorAll('.quick-action-card').forEach((card, index) => {
            setTimeout(() => {
                card.classList.add('fade-in');
            }, 500 + (index * 100));
        });

        // Load user preferences
        loadUserPreferences();
        
        // Start medication reminder system
        startMedicationReminders();
    }

    function setupEventListeners() {
        // Language selection
        document.querySelectorAll('[data-lang]').forEach(item => {
            item.addEventListener('click', function(e) {
                e.preventDefault();
                changeLanguage(this.dataset.lang);
            });
        });

        // Chat functionality
        const chatInput = document.getElementById('chat-input');
        const sendButton = document.getElementById('send-chat');
        
        if (chatInput && sendButton) {
            sendButton.addEventListener('click', sendChatMessage);
            chatInput.addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    sendChatMessage();
                }
            });
        }

        // Emergency form
        const emergencyForm = document.getElementById('emergency-form');
        const submitEmergency = document.getElementById('submit-emergency');
        
        if (submitEmergency) {
            submitEmergency.addEventListener('click', handleEmergencySubmit);
        }

        // Voice call functionality
        const voiceCallBtn = document.getElementById('voice-call-btn');
        if (voiceCallBtn) {
            voiceCallBtn.addEventListener('click', initiateVoiceCall);
        }

        // Barcode scanner
        const startScanner = document.getElementById('start-scanner');
        if (startScanner) {
            startScanner.addEventListener('click', startBarcodeScanner);
        }

        // Quick action cards click handlers
        document.querySelectorAll('.quick-action-card').forEach(card => {
            card.addEventListener('click', function() {
                handleQuickAction(this);
            });
        });
    }

    function changeLanguage(langCode) {
        currentLanguage = langCode;
        document.getElementById('current-language').textContent = getLanguageName(langCode);
        
        // Update UI text based on selected language
        updateUILanguage();
        
        // Save preference
        localStorage.setItem('preferred_language', langCode);
        
        // Show success message
        showNotification(`Language changed to ${getLanguageName(langCode)}`, 'success');
    }

    function getLanguageName(code) {
        const languages = {
            'en': 'English',
            'zu': 'isiZulu',
            'xh': 'isiXhosa',
            'af': 'Afrikaans',
            'st': 'Sesotho',
            'tn': 'Setswana',
            've': 'Tshivenda',
            'ts': 'Xitsonga',
            'ss': 'siSwati',
            'nr': 'isiNdebele',
            'nso': 'Sepedi'
        };
        return languages[code] || 'English';
    }

    function updateUILanguage() {
        const trans = translations[currentLanguage] || translations['en'];
        
        // Update chat bot message
        const botMessage = document.querySelector('.bot-message .message-content p');
        if (botMessage) {
            botMessage.textContent = trans.how_are_you;
        }
    }

    function sendChatMessage() {
        const input = document.getElementById('chat-input');
        const message = input.value.trim();
        
        if (message) {
            addChatMessage(message, 'user');
            input.value = '';
            
            // Simulate bot response
            setTimeout(() => {
                handleBotResponse(message);
            }, 1000);
        }
    }

    function addChatMessage(message, sender) {
        const chatContainer = document.getElementById('chat-container');
        const messageDiv = document.createElement('div');
        messageDiv.className = `chat-message ${sender}-message`;
        
        messageDiv.innerHTML = `
            <div class="message-content">
                <p>${message}</p>
                <small class="text-muted">${new Date().toLocaleTimeString()}</small>
            </div>
        `;
        
        chatContainer.appendChild(messageDiv);
        chatContainer.scrollTop = chatContainer.scrollHeight;
        
        // Store message
        chatMessages.push({
            message: message,
            sender: sender,
            timestamp: new Date(),
            userId: 'current_user_id' // This would come from authentication
        });
    }

    function handleBotResponse(userMessage) {
        let response = "Thank you for sharing. I'm here to help you with your health journey.";
        
        // Simple keyword detection for serious cases
        const seriousKeywords = ['pain', 'chest', 'breathing', 'dizzy', 'emergency', 'help', 'urgent'];
        const medicationKeywords = ['medication', 'pills', 'medicine', 'dose', 'tablet'];
        
        if (seriousKeywords.some(keyword => userMessage.toLowerCase().includes(keyword))) {
            response = "I'm concerned about what you've shared. I'm flagging this as a serious case and notifying the nearest clinic. Please stay calm, help is on the way.";
            flagSeriousCase(userMessage);
        } else if (medicationKeywords.some(keyword => userMessage.toLowerCase().includes(keyword))) {
            response = "I can help you with medication reminders. Have you taken your prescribed medication today?";
        } else if (userMessage.toLowerCase().includes('better')) {
            response = "I'm so glad to hear you're feeling better! Your recovery brings joy to our Ubuntu community. Keep taking care of yourself.";
        }
        
        addChatMessage(response, 'bot');
    }

    function flagSeriousCase(message) {
        // This would integrate with backend to notify healthcare providers
        console.log('SERIOUS CASE FLAGGED:', message);
        showNotification('Emergency services have been notified', 'warning');
        
        // Start monitoring every 10 minutes
        startEmergencyMonitoring();
    }

    function startEmergencyMonitoring() {
        const monitoringInterval = setInterval(() => {
            addChatMessage("How are you feeling now? I'm checking on you every 10 minutes until you receive treatment.", 'bot');
        }, 600000); // 10 minutes

        // Stop monitoring after treatment (this would be triggered by healthcare provider)
        setTimeout(() => {
            clearInterval(monitoringInterval);
            addChatMessage("I can see the doctor is attending to you. I hope you get well soon and know that we love you and you are very valuable. Love you! 💚", 'bot');
        }, 3600000); // Stop after 1 hour for demo
    }

    function startMedicationReminders() {
        // Simulate medication schedule
        medicationReminders = [
            { name: 'Amlodipine', time: '08:00', taken: false },
            { name: 'Metformin', time: '08:00', taken: false },
            { name: 'Metformin', time: '18:00', taken: false },
            { name: 'Vitamin D3', time: '20:00', taken: false }
        ];

        // Check for medication reminders every minute (for demo purposes)
        setInterval(checkMedicationReminders, 60000);
    }

    function checkMedicationReminders() {
        const now = new Date();
        const currentTime = now.getHours().toString().padStart(2, '0') + ':' + now.getMinutes().toString().padStart(2, '0');
        
        medicationReminders.forEach(reminder => {
            if (reminder.time === currentTime && !reminder.taken) {
                showMedicationReminder(reminder);
            }
        });
    }

    function showMedicationReminder(medication) {
        const message = `Time to take your ${medication.name}! Please confirm when you've taken it.`;
        addChatMessage(message, 'bot');
        
        // Add confirmation button
        const chatContainer = document.getElementById('chat-container');
        const confirmDiv = document.createElement('div');
        confirmDiv.className = 'medication-confirm';
        confirmDiv.innerHTML = `
            <button class="btn btn-success btn-sm" onclick="confirmMedication('${medication.name}')">
                <i class="fas fa-check"></i> Taken
            </button>
        `;
        chatContainer.appendChild(confirmDiv);
    }

    // Make function global for onclick
    window.confirmMedication = function(medicationName) {
        const reminder = medicationReminders.find(r => r.name === medicationName);
        if (reminder) {
            reminder.taken = true;
            addChatMessage(`Thank you for confirming you've taken your ${medicationName}. Keep up the good work with your health!`, 'bot');
        }
        
        // Remove confirmation button
        document.querySelector('.medication-confirm').remove();
    };

    function handleEmergencySubmit() {
        const form = document.getElementById('emergency-form');
        const formData = new FormData(form);
        
        const emergencyData = {
            name: document.getElementById('emergency-name').value,
            surname: document.getElementById('emergency-surname').value,
            id: document.getElementById('emergency-id').value,
            type: document.getElementById('emergency-type').value,
            description: document.getElementById('emergency-description').value,
            timestamp: new Date(),
            location: 'Current Location' // Would use geolocation
        };

        // Simulate emergency submission
        console.log('Emergency submitted:', emergencyData);
        
        showNotification('Emergency alert sent! Help is on the way.', 'success');
        
        // Close modal
        const modal = bootstrap.Modal.getInstance(document.getElementById('emergencyModal'));
        modal.hide();
        
        // Clear form
        form.reset();
    }

    function initiateVoiceCall() {
        showNotification('Connecting to Ubuntu Health Assistant...', 'info');
        
        // Simulate voice call setup
        setTimeout(() => {
            showNotification('Voice call connected! Speak now.', 'success');
        }, 2000);
    }

    function startBarcodeScanner() {
        showNotification('Camera starting...', 'info');
        
        // Simulate barcode scanning
        setTimeout(() => {
            const mockMedicationData = {
                name: 'Amlodipine 5mg',
                manufacturer: 'Pharma SA',
                expiry: '2025-12-31',
                batch: 'SA2024001'
            };
            
            displayMedicationInfo(mockMedicationData);
        }, 3000);
    }

    function displayMedicationInfo(data) {
        document.getElementById('med-name').textContent = data.name;
        document.getElementById('med-manufacturer').textContent = data.manufacturer;
        document.getElementById('med-expiry').textContent = data.expiry;
        document.getElementById('med-batch').textContent = data.batch;
        
        document.getElementById('scanner-result').style.display = 'block';
        showNotification('Medication information loaded!', 'success');
    }

    function handleQuickAction(card) {
        const cardClasses = card.className;
        
        if (cardClasses.includes('tips-card')) {
            showDailyTip();
        } else if (cardClasses.includes('appointments-card')) {
            showAppointments();
        } else if (cardClasses.includes('medications-card')) {
            showMedications();
        }
    }

    function showDailyTip() {
        const tips = [
            "Ubuntu teaches us that our well-being is connected to others. Share a healthy meal with family today.",
            "Take a walk in nature - South Africa's beautiful landscapes can heal both body and soul.",
            "Remember: 'I am because we are' - your health journey affects your whole community.",
            "Practice gratitude today - it's good for your mental health and Ubuntu spirit.",
            "Stay hydrated with clean water - a basic right for all South Africans."
        ];
        
        const randomTip = tips[Math.floor(Math.random() * tips.length)];
        showNotification(randomTip, 'info');
    }

    function showAppointments() {
        showNotification('Loading your appointments...', 'info');
        // Would navigate to appointments page
    }

    function showMedications() {
        showNotification('Loading your medications...', 'info');
        // Would navigate to medications page
    }

    function initializeHealthCharts() {
        // Blood pressure chart
        const bpCtx = document.getElementById('bpChart');
        if (bpCtx) {
            new Chart(bpCtx, {
                type: 'line',
                data: {
                    labels: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri'],
                    datasets: [{
                        data: [120, 118, 122, 119, 120],
                        borderColor: '#DE3831',
                        backgroundColor: 'rgba(222, 56, 49, 0.1)',
                        tension: 0.4
                    }]
                },
                options: {
                    responsive: true,
                    plugins: { legend: { display: false } },
                    scales: { x: { display: false }, y: { display: false } }
                }
            });
        }

        // Glucose chart
        const glucoseCtx = document.getElementById('glucoseChart');
        if (glucoseCtx) {
            new Chart(glucoseCtx, {
                type: 'line',
                data: {
                    labels: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri'],
                    datasets: [{
                        data: [5.8, 6.1, 5.9, 6.0, 5.8],
                        borderColor: '#002395',
                        backgroundColor: 'rgba(0, 35, 149, 0.1)',
                        tension: 0.4
                    }]
                },
                options: {
                    responsive: true,
                    plugins: { legend: { display: false } },
                    scales: { x: { display: false }, y: { display: false } }
                }
            });
        }
    }

    function startChatMonitoring() {
        // Check for new messages every 30 seconds
        setInterval(() => {
            // This would check for new messages from healthcare providers
            console.log('Monitoring chat for updates...');
        }, 30000);
    }

    function loadUserPreferences() {
        const savedLanguage = localStorage.getItem('preferred_language');
        if (savedLanguage) {
            changeLanguage(savedLanguage);
        }
    }

    function showNotification(message, type = 'info') {
        const notification = document.createElement('div');
        notification.className = `alert alert-${type} notification-toast`;
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 9999;
            min-width: 300px;
            animation: slideIn 0.3s ease-out;
        `;
        notification.innerHTML = `
            <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'warning' ? 'exclamation-triangle' : 'info-circle'}"></i>
            ${message}
        `;
        
        document.body.appendChild(notification);
        
        setTimeout(() => {
            notification.style.animation = 'slideOut 0.3s ease-in';
            setTimeout(() => notification.remove(), 300);
        }, 5000);
    }

    // Add notification animations
    const notificationStyle = document.createElement('style');
    notificationStyle.textContent = `
        @keyframes slideIn {
            from { transform: translateX(100%); opacity: 0; }
            to { transform: translateX(0); opacity: 1; }
        }
        @keyframes slideOut {
            from { transform: translateX(0); opacity: 1; }
            to { transform: translateX(100%); opacity: 0; }
        }
        .notification-toast {
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
            border: none;
            border-radius: 10px;
        }
    `;
    document.head.appendChild(notificationStyle);

    // Ubuntu spirit message
    console.log('🇿🇦 Ubuntu Healthcare Dashboard Loaded - "I am because we are" 🇿🇦');
});
